// Chart rendering functions for HR Performance Evaluation System
// Grade Colors Updated: v4.0 - New Thresholds Applied

// Create gauge chart for appraisal score
function createGaugeChart(canvasId, score, label = 'Overall Score') {
    const canvas = document.getElementById(canvasId);
    if (!canvas) return null;
    
    // Determine color based on total score (updated grade thresholds)
    let color;
    if (score < 60) color = '#dc3545'; // Red - Poor
    else if (score < 70) color = '#fd7e14'; // Orange - Need Improvement
    else if (score < 80) color = '#198754'; // Green - Meet Requirements
    else if (score < 90) color = '#0d6efd'; // Blue - Very Good
    else color = '#ffc107'; // Yellow - Excellent
    
    // Create gauge chart
    return new Chart(canvas, {
        type: 'doughnut',
        data: {
            datasets: [{
                data: [score, 100 - score],
                backgroundColor: [color, '#e9ecef'],
                borderWidth: 0
            }]
        },
        options: {
            circumference: 180,
            rotation: -90,
            cutout: '80%',
            plugins: {
                tooltip: {
                    enabled: false
                },
                legend: {
                    display: false
                },
                title: {
                    display: true,
                    text: label,
                    position: 'bottom',
                    font: {
                        size: 16
                    }
                }
            },
            layout: {
                padding: {
                    bottom: 30
                }
            },
            animation: {
                animateRotate: true,
                animateScale: true
            }
        },
        plugins: [{
            id: 'centerText',
            afterDraw: function(chart) {
                const width = chart.width;
                const height = chart.height;
                const ctx = chart.ctx;
                
                ctx.restore();
                ctx.font = 'bold 24px Arial';
                ctx.textBaseline = 'middle';
                ctx.textAlign = 'center';
                ctx.fillStyle = color;
                
                // Display score with percentage sign
                const text = `${score.toFixed(1)}%`;
                const textX = width / 2;
                const textY = height - 30;
                
                ctx.fillText(text, textX, textY);
                
                // Display grade text
                ctx.font = 'bold 16px Arial';
                const grade = appUtils.getGrade(score);
                ctx.fillText(grade, textX, textY + 25);
                
                ctx.save();
            }
        }]
    });
}

// Create bar chart for comparing scores
function createBarChart(canvasId, labels, data, title = 'Performance Comparison', customOptions = {}) {
    const canvas = document.getElementById(canvasId);
    if (!canvas) return null;

    // Generate colors based on total scores (grade calculation should be based on total evaluation %)
    const colors = data.map(score => {
        if (score < 65) return '#dc3545'; // Red - Poor
        else if (score < 75) return '#fd7e14'; // Orange - Need Improvement
        else if (score < 85) return '#198754'; // Green - Meets Requirements
        else if (score < 95) return '#0d6efd'; // Blue - Very Good
        else return '#ffc107'; // Yellow - Excellent
    });

    // Default options
    const defaultOptions = {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                max: 100,
                title: {
                    display: true,
                    text: 'Score (%)'
                }
            },
            x: {
                title: {
                    display: true,
                    text: 'Categories'
                }
            }
        },
        plugins: {
            legend: {
                display: false
            },
            title: {
                display: true,
                text: title,
                font: {
                    size: 16
                }
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        const score = context.raw;
                        const grade = appUtils.getGrade(score);
                        return [`Score: ${score.toFixed(1)}%`, `Grade: ${grade}`];
                    }
                }
            }
        }
    };

    // Merge custom options with defaults
    const mergedOptions = { ...defaultOptions, ...customOptions };
    if (customOptions.scales) {
        mergedOptions.scales = { ...defaultOptions.scales, ...customOptions.scales };
    }
    if (customOptions.plugins) {
        mergedOptions.plugins = { ...defaultOptions.plugins, ...customOptions.plugins };
    }

    return new Chart(canvas, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: customOptions.dataLabel || 'Score (%)',
                data: data,
                backgroundColor: colors,
                borderColor: colors,
                borderWidth: 1
            }]
        },
        options: mergedOptions
    });
}

// Create line chart for trend analysis
function createLineChart(canvasId, labels, datasets, title = 'Performance Trend', customOptions = {}) {
    const canvas = document.getElementById(canvasId);
    if (!canvas) return null;

    // Default options
    const defaultOptions = {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                max: 100,
                title: {
                    display: true,
                    text: 'Score (%)'
                }
            },
            x: {
                title: {
                    display: true,
                    text: 'Time Period'
                }
            }
        },
        plugins: {
            title: {
                display: true,
                text: title,
                font: {
                    size: 16
                }
            },
            legend: {
                display: true,
                position: 'top'
            }
        }
    };

    // Merge custom options with defaults
    const mergedOptions = { ...defaultOptions, ...customOptions };
    if (customOptions.scales) {
        mergedOptions.scales = { ...defaultOptions.scales, ...customOptions.scales };
    }
    if (customOptions.plugins) {
        mergedOptions.plugins = { ...defaultOptions.plugins, ...customOptions.plugins };
    }

    return new Chart(canvas, {
        type: 'line',
        data: {
            labels: labels,
            datasets: datasets
        },
        options: mergedOptions
    });
}

// Create pie chart for distribution analysis
function createPieChart(canvasId, labels, data, title = 'Grade Distribution', customColors = null) {
    const canvas = document.getElementById(canvasId);
    if (!canvas) return null;

    // Use custom colors if provided, otherwise use default colors
    const colors = customColors || [
        '#dc3545', // Poor - Red
        '#fd7e14', // Need Improvement - Orange
        '#198754', // Meets Requirements - Green
        '#0d6efd', // Very Good - Blue
        '#ffc107'  // Excellent - Yellow
    ];

    return new Chart(canvas, {
        type: 'pie',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: colors,
                borderColor: '#ffffff',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'right'
                },
                title: {
                    display: true,
                    text: title,
                    font: {
                        size: 16
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.raw;
                            const percentage = ((value / context.dataset.data.reduce((a, b) => a + b, 0)) * 100).toFixed(1);
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
}

// Create radar chart for KPI category comparison
function createRadarChart(canvasId, labels, datasets, title = 'KPI Category Performance') {
    const canvas = document.getElementById(canvasId);
    if (!canvas) return null;
    
    return new Chart(canvas, {
        type: 'radar',
        data: {
            labels: labels,
            datasets: datasets
        },
        options: {
            responsive: true,
            scales: {
                r: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        stepSize: 20
                    }
                }
            },
            plugins: {
                title: {
                    display: true,
                    text: title,
                    font: {
                        size: 16
                    }
                }
            }
        }
    });
}

// Export chart functions
window.appCharts = {
    createGaugeChart,
    createBarChart,
    createLineChart,
    createPieChart,
    createRadarChart
};