# HR Performance Evaluation System

A web-based HR performance evaluation system built with HTML, CSS, JavaScript, and Supabase SQL.

## Overview

This application allows HR administrators and managers to conduct performance evaluations for employees. It features role-based access control, KPI management, appraisal workflows, dashboards, and reporting capabilities.

## Features

- **User Roles**: Admin and Manager roles with appropriate permissions
- **Employee Management**: Add, edit, and delete employees
- **KPI Management**: Define and assign KPIs to employees with customizable weights
- **Appraisal System**: Conduct performance evaluations with weighted scoring
- **Weight Management**: Control weights at both category level (Performance vs. Behavioral) and individual KPI level
- **Dashboards**: View performance analytics and reports
- **PDF Generation**: Generate and download appraisal reports as PDFs
- **Data Export**: Export data to Excel
- **Dark/Light Mode**: Toggle between dark and light themes
- **Mobile Responsive**: Works on all device sizes

## Technology Stack

- **Frontend**: HTML5, CSS3, JavaScript (Vanilla JS), TailwindCSS
- **Charts**: Chart.js
- **PDF Generation**: jsPDF
- **Excel Export**: SheetJS (XLSX)
- **Database**: Supabase (PostgreSQL)
- **Hosting**: GitHub Pages

## Testing Instructions

### Prerequisites

1. Set up a Supabase account and create a new project
2. Run the SQL script in `supabase/simplified_schema.sql` to create the database schema
3. Update the Supabase URL and anon key in `assets/js/supabase.js`

For detailed setup instructions, please refer to the [SETUP.md](SETUP.md) file.

### Test Accounts

The system comes with three test accounts:

1. **Admin**
   - Username: admin
   - Password: admin123

2. **Parent Manager**
   - Username: parent_manager
   - Password: manager123

3. **Child Manager**
   - Username: child_manager
   - Password: manager456

### Testing Workflow

#### 1. Admin Testing

1. **Login as Admin**
   - Go to the login page and enter admin credentials
   - You should be redirected to the admin dashboard

2. **Employee Management**
   - Navigate to the Employees page
   - Try adding a new employee
   - Edit an existing employee
   - Delete an employee (if needed)
   - Assign KPIs to an employee

3. **KPI Management**
   - Navigate to the KPIs page
   - Add new KPIs
   - Edit existing KPIs
   - Assign KPIs to employees

4. **View Reports**
   - Navigate to the Reports page
   - View company-wide performance analytics
   - Filter data by department, position, manager, or period
   - Export data to Excel

#### 2. Manager Testing

1. **Login as Parent Manager**
   - Go to the login page and enter parent_manager credentials
   - You should be redirected to the manager dashboard

2. **View Team**
   - Navigate to the My Team page
   - Verify you can see all direct reports and sub-teams

3. **Conduct Appraisals**
   - Navigate to the Appraisals page
   - Start a new appraisal for a direct report
   - Score KPIs and submit the appraisal
   - View the completed appraisal
   - Download the appraisal as PDF

4. **View Reports**
   - Navigate to the Reports page
   - View team performance analytics
   - Filter data by employee or period
   - Export data to Excel

5. **Login as Child Manager**
   - Log out and log in as child_manager
   - Verify you can only see and appraise your direct reports

### Testing Features

#### Dark Mode

- Click the moon/sun icon in the bottom right corner to toggle between dark and light modes
- Verify the preference is saved between sessions

#### Mobile Responsiveness

- Test the application on different device sizes (desktop, tablet, mobile)
- Verify all features work correctly on smaller screens

#### Data Export

- Test exporting tables to Excel from various pages
- Verify the exported data is correct

#### PDF Generation

- Generate PDF reports for appraisals
- Verify all data is correctly displayed in the PDF

## KPI Weight Management

The system provides a flexible approach to weight management:

1. **Category Level Weights**:
   - Adjust the distribution between Performance KPIs and Behavioral KPIs
   - Default is 80% Performance and 20% Behavioral
   - Can be customized per employee (e.g., 70/30, 60/40)
   - Set these weights in the "Assign KPIs" page for each employee

2. **Individual KPI Weights**:
   - Within each category, assign specific weights to individual KPIs
   - Weights within a category must sum to 100%
   - Allows for emphasizing certain KPIs over others based on role

This two-level weighting system ensures accurate performance evaluation tailored to each role and department.

## Troubleshooting

- **Login Issues**: Ensure the Supabase URL and anon key are correctly set in `assets/js/supabase.js`
- **Database Errors**: Check the browser console for error messages
- **PDF Generation Errors**: Ensure jsPDF is properly loaded
- **404 Errors**: Make sure all HTML files are in the correct directories
- **Supabase Client Issues**: Verify you're using the UMD version of the Supabase client

## Future Enhancements

- Email notifications for pending appraisals
- Self-assessment functionality for employees
- Goal setting and tracking
- Integration with HRIS systems
- Advanced analytics and reporting