<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - HR Performance Evaluation System</title>
    <!-- Grade Chart Fixed: v4.1 - Performance Grade Chart Data Loading Fixed -->
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- SheetJS (XLSX) -->
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/styles.css">

    <style>
        /* Modern color palette for charts */
        :root {
            --chart-primary: #4a90e2;
            --chart-secondary: #7bb3f0;
            --chart-success: #27ae60;
            --chart-info: #3498db;
            --chart-warning: #f39c12;
            --chart-danger: #e74c3c;
            --chart-purple: #9b59b6;
            --chart-orange: #e67e22;
            --chart-teal: #1abc9c;
            --chart-pink: #e91e63;
        }

        /* Interactive filter button styles */
        .filter-btn-active {
            background-color: #e74c3c !important;
            border-color: #e74c3c !important;
            color: white !important;
        }

        /* Modern card styling */
        .card {
            border-radius: 8px;
            border: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 15px;
        }

        .card-header {
            background: linear-gradient(135deg, #4a90e2 0%, #7bb3f0 100%);
            color: white;
            border-radius: 8px 8px 0 0;
            border: none;
            padding: 10px 15px;
        }

        .card-body {
            padding: 10px;
        }

        /* Compact chart containers */
        .chart-container-compact {
            position: relative;
            height: 280px;
            margin: 10px 0;
        }

        .chart-container {
            position: relative;
            height: 280px;
            margin: 10px 0;
        }

        /* Filter section styling */
        .filter-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .stat-card {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        /* Navigation button press effects */
        .nav-link {
            transition: all 0.3s ease;
            transform: scale(1);
        }

        .nav-link.pressed {
            transform: scale(0.9);
            background-color: #0056b3 !important;
            color: white !important;
        }

        .nav-link.active {
            transform: scale(0.9);
            background-color: #0056b3 !important;
            color: white !important;
        }

        /* Dark mode support */
        body.dark-mode .filter-section {
            background: #2d2d2d;
            border: 1px solid #444444;
            color: #e0e0e0;
        }

        body.dark-mode .card {
            background-color: #1e1e1e;
            border-color: #333333;
            color: #e0e0e0;
        }

        body.dark-mode .card-header {
            background: linear-gradient(135deg, #4a90e2 0%, #7bb3f0 100%);
            border-color: #333333;
            color: white;
        }

        body.dark-mode .table {
            color: #e0e0e0;
        }

        body.dark-mode .table thead th {
            background-color: rgba(255, 255, 255, 0.05);
            color: #e0e0e0;
        }

        body.dark-mode .form-label {
            color: #e0e0e0;
        }

        body.dark-mode h1, body.dark-mode h2, body.dark-mode h3, body.dark-mode h5 {
            color: #e0e0e0;
        }

        /* New header layout */
        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 0.75rem;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        /* Logo styles handled by global CSS */

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-name {
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
        }

        .logout-btn {
            color: white;
            text-decoration: none;
            padding: 0.4rem 0.8rem;
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }

        .nav-bottom {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .nav-bottom .nav-link {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }

        /* Table text color fixes for dark mode */
        body.dark-mode .table td {
            color: #e0e0e0;
        }

        body.dark-mode .table tbody tr {
            color: #e0e0e0;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-top">
                <div class="logo-section">
                    <i class="fas fa-chart-line logo-icon"></i>
                    <span class="logo-text">Performance Evaluation System</span>
                </div>
                <div class="user-info">
                    <span id="currentUserName" class="user-name">Loading...</span>
                    <a href="#" id="logoutBtn" class="logout-btn">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
            </div>
            <nav class="nav-bottom">
                <a href="index.html" class="nav-link active">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
                <a href="employees.html" class="nav-link">
                    <i class="fas fa-users"></i> Employees
                </a>
                <a href="kpis.html" class="nav-link">
                    <i class="fas fa-bullseye"></i> KPIs
                </a>
                <a href="assign-kpis.html" class="nav-link">
                    <i class="fas fa-user-tag"></i> Assign KPIs
                </a>
                <a href="assign-appraisals.html" class="nav-link">
                    <i class="fas fa-clipboard-list"></i> Assign Appraisals
                </a>
                <a href="reports.html" class="nav-link">
                    <i class="fas fa-chart-bar"></i> Reports
                </a>
            </nav>
        </div>
    </header>
    
    <!-- Main content -->
    <main class="main">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Admin Dashboard</h1>
                <div>
                    <button id="createPeriodsBtn" class="btn btn-success me-2">
                        <i class="fas fa-calendar-plus mr-1"></i> Create Periods
                    </button>
                    <button id="exportDashboardBtn" class="btn btn-primary">
                        <i class="fas fa-download mr-1"></i> Export Data
                    </button>
                    <button id="refreshDashboardBtn" class="btn btn-secondary ml-2">
                        <i class="fas fa-sync mr-1"></i> Refresh
                    </button>
                </div>
            </div>
            
            <!-- Summary Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-value" id="totalEmployees">0</div>
                        <div class="stat-label">Total Employees</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-value" id="averagePerformance">0%</div>
                        <div class="stat-label">Average Performance %</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-value" id="averageBehaviors">0%</div>
                        <div class="stat-label">Average Behaviors %</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-value" id="averageTotal">0%</div>
                        <div class="stat-label">Average Total Evaluation %</div>
                    </div>
                </div>
            </div>
            
            <!-- Filters -->
            <div class="card mb-3 filter-section">
                <div class="card-body py-2">
                    <div class="row g-2">
                        <div class="col-md-2">
                            <label for="periodFilter" class="form-label small">Period</label>
                            <select id="periodFilter" class="form-select form-select-sm">
                                <option value="">All Periods</option>
                                <!-- Periods will be loaded dynamically -->
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="departmentFilter" class="form-label small">Department</label>
                            <select id="departmentFilter" class="form-select form-select-sm">
                                <option value="">All Departments</option>
                                <!-- Departments will be loaded dynamically -->
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="positionFilter" class="form-label small">Position</label>
                            <select id="positionFilter" class="form-select form-select-sm">
                                <option value="">All Positions</option>
                                <!-- Positions will be loaded dynamically -->
                            </select>
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button id="applyFilters" class="btn btn-primary btn-sm me-2">
                                <i class="fas fa-filter"></i> Apply
                            </button>
                            <button id="resetFilters" class="btn btn-secondary btn-sm">
                                <i class="fas fa-times"></i> Reset
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts -->
            <div class="row">
                <!-- Performance Grade Distribution -->
                <div class="col-md-6">
                    <div class="report-card">
                        <div class="card-header py-2">
                            <h5 class="mb-0">Performance Grade</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container-compact">
                                <canvas id="gradeDistributionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Department Performance -->
                <div class="col-md-6">
                    <div class="report-card">
                        <div class="card-header py-2">
                            <h5 class="mb-0">Department Performance</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container-compact">
                                <canvas id="departmentPerformanceChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>



            <div class="row">
                <!-- Performance Bell Curve and Performance Over Period -->
                <div class="col-md-6">
                    <div class="report-card">
                        <div class="card-header py-2">
                            <h5 class="mb-0">Performance Bell Curve</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container-compact">
                                <canvas id="performanceBellCurve"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="report-card">
                        <div class="card-header py-2">
                            <h5 class="mb-0">Performance Over Period</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container-compact">
                                <canvas id="performanceOverPeriod"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Top 5 Performance KPIs -->
                <div class="col-md-6">
                    <div class="report-card">
                        <div class="card-header py-2">
                            <h5 class="mb-0">Top 5 Performance KPIs Scores</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container-compact">
                                <canvas id="topPerformanceKPIsChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Top 5 Behavior KPIs -->
                <div class="col-md-6">
                    <div class="report-card">
                        <div class="card-header py-2">
                            <h5 class="mb-0">Top 5 Behavior KPIs Scores</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container-compact">
                                <canvas id="topBehaviorKPIsChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Recent appraisals table -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center py-2">
                    <h5 class="mb-0">Recent Appraisals</h5>
                    <div class="d-flex align-items-center gap-2">
                        <select id="tablePeriodFilter" class="form-select form-select-sm" style="width: 120px;">
                            <option value="">All Periods</option>
                            <!-- Periods will be loaded dynamically -->
                        </select>
                        <select id="tableManagerFilter" class="form-select form-select-sm" style="width: 120px;">
                            <option value="">All Managers</option>
                            <!-- Managers will be loaded dynamically -->
                        </select>
                        <button id="applyTableFilters" class="btn btn-primary btn-sm">
                            <i class="fas fa-filter"></i>
                        </button>
                        <button id="resetTableFilters" class="btn btn-secondary btn-sm">
                            <i class="fas fa-times"></i>
                        </button>
                        <button id="exportAppraisals" class="btn btn-light btn-sm">
                            <i class="fas fa-download me-1"></i> Export to Excel
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="appraisalsTable" class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Employee</th>
                                    <th>Position</th>
                                    <th>Department</th>
                                    <th>Manager</th>
                                    <th>Period</th>
                                    <th style="cursor: pointer;" onclick="sortTableByScore()">
                                        Score
                                        <i id="sortIcon" class="fas fa-sort"></i>
                                    </th>
                                    <th>Grade</th>
                                    <th>Status</th>
                                    <th style="cursor: pointer;" onclick="sortTableByDate()">
                                        Date <i id="dateSortIcon" class="fas fa-sort"></i>
                                    </th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="appraisalsTableBody">
                                <!-- Appraisals will be loaded dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p class="footer-text">© Produced by: Dr. Ahmed Atef - All rights reserved.</p>
        </div>
    </footer>
    
    <!-- Dark mode toggle -->
    <div id="darkModeToggle" class="dark-mode-toggle" title="Switch to Dark Mode">
        <i class="fas fa-moon"></i>
    </div>
    
    <!-- Supabase JS -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2/dist/umd/supabase.js"></script>
    
    <!-- Custom JS -->
    <script src="../assets/js/utils.js"></script>
    <script src="../assets/js/supabase.js"></script>
    <script src="../assets/js/charts.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication and role
            if (!appAuth.checkAuth('admin')) {
                return;
            }

            // Load current user name
            loadCurrentUserName();

            // Initialize enhanced navigation
            appUtils.initializeNavigation();

            // Apply dark mode if previously enabled
            appUtils.applyDarkMode();
            
            // Dark mode toggle
            document.getElementById('darkModeToggle').addEventListener('click', function() {
                appUtils.toggleDarkMode();
            });
            
            // Logout button
            document.getElementById('logoutBtn').addEventListener('click', function(e) {
                e.preventDefault();
                appAuth.logoutUser();
            });
            
            // Fix any inconsistent grade data first
            fixInconsistentGrades();

            // Load dashboard data
            loadDashboardData();
            
            // Load filters
            loadFilters();
            loadTableFilters();
            
            // Apply filters button
            document.getElementById('applyFilters').addEventListener('click', function() {
                // Add active state to apply button
                this.classList.add('filter-btn-active');
                document.getElementById('resetFilters').classList.remove('filter-btn-active');
                loadDashboardData();
            });

            // Reset filters button
            document.getElementById('resetFilters').addEventListener('click', function() {
                // Reset filter values
                document.getElementById('periodFilter').value = '';
                document.getElementById('departmentFilter').value = '';
                document.getElementById('positionFilter').value = '';

                // Remove active state from apply button and restore normal state
                document.getElementById('applyFilters').classList.remove('filter-btn-active');
                this.classList.remove('filter-btn-active');

                loadDashboardData();
            });
            
            // Export dashboard data button
            document.getElementById('exportDashboardBtn').addEventListener('click', function() {
                appUtils.exportToExcel('appraisalsTable', 'Admin_Dashboard_Report');
            });

            // Refresh dashboard button
            document.getElementById('refreshDashboardBtn').addEventListener('click', function() {
                loadDashboardData();
                appUtils.showNotification('Dashboard refreshed successfully', 'success');
            });

            // Create periods button
            document.getElementById('createPeriodsBtn').addEventListener('click', function() {
                // Set default year to next year
                const nextYear = new Date().getFullYear() + 1;
                document.getElementById('yearInput').value = nextYear;

                // Show modal
                const modal = new bootstrap.Modal(document.getElementById('createPeriodsModal'));
                modal.show();
            });

            // Confirm create periods
            document.getElementById('confirmCreatePeriods').addEventListener('click', function() {
                createAppraisalPeriods();
            });

            // Table filter dropdowns - auto-apply on change
            document.getElementById('tablePeriodFilter').addEventListener('change', function() {
                filterAppraisalsTable();
            });

            document.getElementById('tableManagerFilter').addEventListener('change', function() {
                filterAppraisalsTable();
            });

            // Table filter buttons
            document.getElementById('applyTableFilters').addEventListener('click', function() {
                filterAppraisalsTable();
                // Add active state to apply button
                this.classList.add('filter-btn-active');
                document.getElementById('resetTableFilters').classList.remove('filter-btn-active');
            });

            document.getElementById('resetTableFilters').addEventListener('click', function() {
                // Reset table filter values
                document.getElementById('tablePeriodFilter').value = '';
                document.getElementById('tableManagerFilter').value = '';

                // Remove active state and restore normal state
                document.getElementById('applyTableFilters').classList.remove('filter-btn-active');
                this.classList.remove('filter-btn-active');

                // Reset date sort
                dateSortAscending = true;
                document.getElementById('dateSortIcon').className = 'fas fa-sort';

                // Show all data (reset to original data)
                populateAppraisalsTable(currentAppraisalsData);
            });
        });

        // Create appraisal periods for a specific year
        async function createAppraisalPeriods() {
            try {
                const year = parseInt(document.getElementById('yearInput').value);
                const quarterlyPeriods = document.getElementById('quarterlyPeriods').checked;
                const semesterPeriods = document.getElementById('semesterPeriods').checked;
                const annualPeriod = document.getElementById('annualPeriod').checked;

                if (!year || year < 2024 || year > 2030) {
                    appUtils.showNotification('Please enter a valid year between 2024 and 2030', 'error');
                    return;
                }

                if (!quarterlyPeriods && !semesterPeriods && !annualPeriod) {
                    appUtils.showNotification('Please select at least one period type to create', 'error');
                    return;
                }

                // Check if periods already exist for this year
                const { data: existingPeriods, error: checkError } = await supabaseClient
                    .from('appraisal_periods')
                    .select('name')
                    .ilike('name', `%${year}%`);

                if (checkError) throw checkError;

                if (existingPeriods && existingPeriods.length > 0) {
                    const existingNames = existingPeriods.map(p => p.name).join(', ');
                    if (!confirm(`Some periods for ${year} already exist: ${existingNames}\n\nDo you want to continue and create additional periods?`)) {
                        return;
                    }
                }

                // Prepare periods to create
                const periodsToCreate = [];

                // Quarterly periods
                if (quarterlyPeriods) {
                    periodsToCreate.push(
                        { name: `Q1 ${year}`, start_date: `${year}-01-01`, end_date: `${year}-03-31`, is_active: true },
                        { name: `Q2 ${year}`, start_date: `${year}-04-01`, end_date: `${year}-06-30`, is_active: true },
                        { name: `Q3 ${year}`, start_date: `${year}-07-01`, end_date: `${year}-09-30`, is_active: true },
                        { name: `Q4 ${year}`, start_date: `${year}-10-01`, end_date: `${year}-12-31`, is_active: true }
                    );
                }

                // Semester periods
                if (semesterPeriods) {
                    periodsToCreate.push(
                        { name: `Semester 1 ${year}`, start_date: `${year}-01-01`, end_date: `${year}-06-30`, is_active: true },
                        { name: `Semester 2 ${year}`, start_date: `${year}-07-01`, end_date: `${year}-12-31`, is_active: true }
                    );
                }

                // Annual period
                if (annualPeriod) {
                    periodsToCreate.push(
                        { name: `Annual ${year}`, start_date: `${year}-01-01`, end_date: `${year}-12-31`, is_active: true }
                    );
                }

                // Create periods in database
                const { error: insertError } = await supabaseClient
                    .from('appraisal_periods')
                    .insert(periodsToCreate);

                if (insertError) throw insertError;

                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('createPeriodsModal'));
                modal.hide();

                // Show success message
                appUtils.showNotification(`Successfully created ${periodsToCreate.length} periods for ${year}!`, 'success');

                // Refresh dashboard to show new periods in filters
                loadDashboardData();

            } catch (error) {
                console.error('Error creating periods:', error);
                appUtils.showNotification('Error creating periods: ' + error.message, 'error');
            }
        }

        // Load dashboard data
        async function loadDashboardData() {
            try {
                console.log('Admin dashboard: Loading dashboard data...');
                // Get filter values
                const departmentFilter = document.getElementById('departmentFilter').value;
                const positionFilter = document.getElementById('positionFilter').value;
                
                // Build query for employees count
                let query = supabaseClient
                    .from('employees')
                    .select('*', { count: 'exact' });
                
                if (departmentFilter) {
                    query = query.eq('department', departmentFilter);
                }
                
                if (positionFilter) {
                    query = query.eq('position', positionFilter);
                }
                

                
                // Execute query
                const { count: employeesCount } = await query;
                
                // Update total employees stat
                document.getElementById('totalEmployees').textContent = employeesCount || 0;
                
                // Build query for appraisals
                let appraisalsQuery = supabaseClient
                    .from('appraisals')
                    .select(`
                        id,
                        employee_code,
                        manager_code,
                        period_id,
                        total_score,
                        performance_score,
                        behavioral_score,
                        grade,
                        manager_signature,
                        employee_signature,
                        employee_signature_requested,
                        created_at,
                        employee:employees!appraisals_employee_code_fkey(code_number, name, department, position),
                        manager:employees!appraisals_manager_code_fkey(code_number, name),
                        period:appraisal_periods(id, name, start_date, end_date)
                    `);
                
                if (departmentFilter) {
                    appraisalsQuery = appraisalsQuery.eq('employee.department', departmentFilter);
                }
                
                if (positionFilter) {
                    appraisalsQuery = appraisalsQuery.eq('employee.position', positionFilter);
                }
                

                
                // Execute query
                console.log('Admin dashboard: Executing appraisals query...');
                const { data: appraisals, error } = await appraisalsQuery;

                if (error) {
                    console.error('Admin dashboard query error:', error);
                    throw error;
                }

                console.log('Admin dashboard: Query result:', appraisals);
                
                // Calculate meaningful analytics
                if (appraisals && appraisals.length > 0) {
                    // Calculate average performance percentage
                    const totalPerformance = appraisals.reduce((sum, appraisal) => sum + (appraisal.performance_score || 0), 0);
                    const averagePerformance = totalPerformance / appraisals.length;
                    document.getElementById('averagePerformance').textContent = `${averagePerformance.toFixed(1)}%`;

                    // Calculate average behavioral percentage
                    const totalBehavioral = appraisals.reduce((sum, appraisal) => sum + (appraisal.behavioral_score || 0), 0);
                    const averageBehavioral = totalBehavioral / appraisals.length;
                    document.getElementById('averageBehaviors').textContent = `${averageBehavioral.toFixed(1)}%`;

                    // Calculate average total evaluation percentage
                    const totalEvaluation = appraisals.reduce((sum, appraisal) => sum + (appraisal.total_score || 0), 0);
                    const averageTotal = totalEvaluation / appraisals.length;
                    document.getElementById('averageTotal').textContent = `${averageTotal.toFixed(1)}%`;
                } else {
                    document.getElementById('averagePerformance').textContent = 'N/A';
                    document.getElementById('averageBehaviors').textContent = 'N/A';
                    document.getElementById('averageTotal').textContent = 'N/A';
                }
                
                // Populate appraisals table
                populateAppraisalsTable(appraisals || []);
                
                // Create charts
                createDashboardCharts(appraisals || []);
                
            } catch (error) {
                console.error('Error loading dashboard data:', error);
                appUtils.showNotification('Error loading dashboard data', 'error');
            }
        }
        
        // Load filter options
        async function loadFilters() {
            try {
                // Load departments
                const { data: departments } = await supabaseClient
                    .from('employees')
                    .select('department')
                    .order('department');
                
                if (departments) {
                    const uniqueDepartments = [...new Set(departments.map(item => item.department))];
                    const departmentFilter = document.getElementById('departmentFilter');
                    
                    uniqueDepartments.forEach(department => {
                        const option = document.createElement('option');
                        option.value = department;
                        option.textContent = department;
                        departmentFilter.appendChild(option);
                    });
                }
                
                // Load positions
                const { data: positions } = await supabaseClient
                    .from('employees')
                    .select('position')
                    .order('position');
                
                if (positions) {
                    const uniquePositions = [...new Set(positions.map(item => item.position))];
                    const positionFilter = document.getElementById('positionFilter');
                    
                    uniquePositions.forEach(position => {
                        const option = document.createElement('option');
                        option.value = position;
                        option.textContent = position;
                        positionFilter.appendChild(option);
                    });
                }

                // Load periods from appraisal_periods table (same as reports page)
                const { data: periods } = await supabaseClient
                    .from('appraisal_periods')
                    .select('id, name')
                    .order('start_date', { ascending: false });

                if (periods) {
                    const periodFilter = document.getElementById('periodFilter');

                    periods.forEach(period => {
                        const option = document.createElement('option');
                        option.value = period.id;
                        option.textContent = period.name;
                        periodFilter.appendChild(option);
                    });
                }

            } catch (error) {
                console.error('Error loading filters:', error);
            }
        }

        // Load table-specific filters
        async function loadTableFilters() {
            try {
                // Load periods for table filter
                const { data: periods } = await supabaseClient
                    .from('appraisal_periods')
                    .select('id, name')
                    .order('start_date', { ascending: false });

                const tablePeriodFilter = document.getElementById('tablePeriodFilter');
                tablePeriodFilter.innerHTML = '<option value="">All Periods</option>';

                if (periods) {
                    periods.forEach(period => {
                        const option = document.createElement('option');
                        option.value = period.id;
                        option.textContent = period.name;
                        tablePeriodFilter.appendChild(option);
                    });
                }

                // Load managers for table filter
                const { data: managers } = await supabaseClient
                    .from('employees')
                    .select('code_number, name')
                    .eq('is_manager', true)
                    .order('name');

                const tableManagerFilter = document.getElementById('tableManagerFilter');
                tableManagerFilter.innerHTML = '<option value="">All Managers</option>';

                if (managers) {
                    managers.forEach(manager => {
                        const option = document.createElement('option');
                        option.value = manager.code_number;
                        option.textContent = manager.name;
                        tableManagerFilter.appendChild(option);
                    });
                }

            } catch (error) {
                console.error('Error loading table filters:', error);
            }
        }

        // Populate appraisals table
        function populateAppraisalsTable(appraisals) {
            // Store current data for sorting
            currentAppraisalsData = appraisals;

            const tableBody = document.getElementById('appraisalsTableBody');
            tableBody.innerHTML = '';

            if (appraisals.length === 0) {
                const row = document.createElement('tr');
                const cell = document.createElement('td');
                cell.colSpan = 9;
                cell.textContent = 'No appraisals found';
                cell.className = 'text-center';
                row.appendChild(cell);
                tableBody.appendChild(row);
                return;
            }
            
            appraisals.forEach(appraisal => {
                const row = document.createElement('tr');
                
                // Employee
                const employeeCell = document.createElement('td');
                employeeCell.textContent = appraisal.employee ? appraisal.employee.name : 'N/A';
                row.appendChild(employeeCell);
                
                // Position
                const positionCell = document.createElement('td');
                positionCell.textContent = appraisal.employee ? appraisal.employee.position : 'N/A';
                row.appendChild(positionCell);
                
                // Department
                const departmentCell = document.createElement('td');
                departmentCell.textContent = appraisal.employee ? appraisal.employee.department : 'N/A';
                row.appendChild(departmentCell);
                
                // Manager
                const managerCell = document.createElement('td');
                managerCell.textContent = appraisal.manager ? appraisal.manager.name : 'N/A';
                row.appendChild(managerCell);
                
                // Period
                const periodCell = document.createElement('td');
                periodCell.textContent = appraisal.period ? appraisal.period.name : 'N/A';
                row.appendChild(periodCell);
                
                // Score
                const scoreCell = document.createElement('td');
                scoreCell.textContent = appraisal.total_score ? `${appraisal.total_score.toFixed(1)}%` : 'N/A';
                row.appendChild(scoreCell);
                
                // Grade
                const gradeCell = document.createElement('td');
                if (appraisal.grade) {
                    let gradeClass = '';
                    switch(appraisal.grade.toLowerCase()) {
                        case 'poor':
                            gradeClass = 'grade-badge grade-poor';
                            break;
                        case 'need improvement':
                        case 'needs improvement':
                            gradeClass = 'grade-badge grade-needs-improvement';
                            break;
                        case 'meet requirements':
                        case 'meets requirements':
                            gradeClass = 'grade-badge grade-meets-requirements';
                            break;
                        case 'very good':
                            gradeClass = 'grade-badge grade-very-good';
                            break;
                        case 'excellent':
                            gradeClass = 'grade-badge grade-excellent';
                            break;
                        default:
                            gradeClass = 'badge bg-secondary';
                    }
                    gradeCell.innerHTML = `<span class="${gradeClass}">${appraisal.grade}</span>`;
                } else {
                    gradeCell.textContent = 'N/A';
                }
                row.appendChild(gradeCell);

                // Status - NEW WORKFLOW
                const statusCell = document.createElement('td');
                let status = 'Draft';
                let statusClass = 'badge bg-warning';

                if (appraisal.manager_signature && appraisal.employee_signature) {
                    status = 'Completed';
                    statusClass = 'badge bg-success';
                } else if (appraisal.manager_signature && appraisal.employee_signature_requested) {
                    status = 'Pending Employee';
                    statusClass = 'badge bg-info';
                } else if (appraisal.manager_signature) {
                    status = 'Submitted';
                    statusClass = 'badge bg-primary';
                }

                statusCell.innerHTML = `<span class="${statusClass}">${status}</span>`;
                row.appendChild(statusCell);

                // Date
                const dateCell = document.createElement('td');
                dateCell.textContent = appraisal.created_at ? appUtils.formatReadableDate(appraisal.created_at) : 'N/A';
                row.appendChild(dateCell);
                
                // Actions
                const actionsCell = document.createElement('td');
                actionsCell.innerHTML = `
                    <a href="view-appraisal.html?id=${appraisal.id}" class="btn btn-info btn-sm me-1">
                        <i class="fas fa-eye"></i>
                    </a>
                    <button class="btn btn-danger btn-sm" onclick="deleteAppraisal('${appraisal.id}', '${appraisal.employee.name}', '${appraisal.period.name}')" title="Delete Appraisal">
                        <i class="fas fa-trash"></i>
                    </button>
                `;
                row.appendChild(actionsCell);
                
                tableBody.appendChild(row);
            });
        }

        // Filter appraisals table based on table-specific filters
        function filterAppraisalsTable() {
            const tablePeriodFilter = document.getElementById('tablePeriodFilter').value;
            const tableManagerFilter = document.getElementById('tableManagerFilter').value;

            console.log('Filtering table:', { period: tablePeriodFilter, manager: tableManagerFilter });

            let filteredData = [...currentAppraisalsData];

            // Apply period filter (only if value is not empty)
            if (tablePeriodFilter && tablePeriodFilter !== '') {
                filteredData = filteredData.filter(appraisal =>
                    appraisal.period && appraisal.period.id == tablePeriodFilter
                );
                console.log('After period filter:', filteredData.length);
            }

            // Apply manager filter (only if value is not empty)
            if (tableManagerFilter && tableManagerFilter !== '') {
                filteredData = filteredData.filter(appraisal =>
                    appraisal.manager_code === tableManagerFilter
                );
                console.log('After manager filter:', filteredData.length);
            }

            // If no filters applied, show all data
            if ((!tablePeriodFilter || tablePeriodFilter === '') && (!tableManagerFilter || tableManagerFilter === '')) {
                console.log('No filters applied, showing all data:', currentAppraisalsData.length);
                filteredData = [...currentAppraisalsData];
            }

            // Repopulate table with filtered data
            populateAppraisalsTable(filteredData);
        }

        // Sort table by date (oldest to newest toggle)
        let dateSortAscending = true;
        function sortTableByDate() {
            const sortIcon = document.getElementById('dateSortIcon');

            // Get current filtered data or all data
            const tablePeriodFilter = document.getElementById('tablePeriodFilter').value;
            const tableManagerFilter = document.getElementById('tableManagerFilter').value;

            let dataToSort = [...currentAppraisalsData];

            // Apply current filters first
            if (tablePeriodFilter) {
                dataToSort = dataToSort.filter(appraisal =>
                    appraisal.period && appraisal.period.id == tablePeriodFilter
                );
            }
            if (tableManagerFilter) {
                dataToSort = dataToSort.filter(appraisal =>
                    appraisal.manager_code === tableManagerFilter
                );
            }

            // Sort by date
            dataToSort.sort((a, b) => {
                const dateA = new Date(a.created_at);
                const dateB = new Date(b.created_at);
                return dateSortAscending ? dateA - dateB : dateB - dateA;
            });

            // Update sort icon
            if (dateSortAscending) {
                sortIcon.className = 'fas fa-sort-up';
            } else {
                sortIcon.className = 'fas fa-sort-down';
            }

            // Toggle sort direction for next click
            dateSortAscending = !dateSortAscending;

            // Repopulate table with sorted data
            populateAppraisalsTable(dataToSort);
        }

        // Create dashboard charts
        function createDashboardCharts(appraisals) {
            // Destroy existing charts to prevent canvas reuse errors
            if (window.gradeDistributionChart && typeof window.gradeDistributionChart.destroy === 'function') {
                window.gradeDistributionChart.destroy();
            }
            if (window.departmentChart && typeof window.departmentChart.destroy === 'function') {
                window.departmentChart.destroy();
            }
            if (window.bellCurveChart && typeof window.bellCurveChart.destroy === 'function') {
                window.bellCurveChart.destroy();
            }
            if (window.periodChart && typeof window.periodChart.destroy === 'function') {
                window.periodChart.destroy();
            }
            if (window.topPerformanceKPIsChart && typeof window.topPerformanceKPIsChart.destroy === 'function') {
                window.topPerformanceKPIsChart.destroy();
            }
            if (window.topBehavioralKPIsChart && typeof window.topBehavioralKPIsChart.destroy === 'function') {
                window.topBehavioralKPIsChart.destroy();
            }

            // Performance Grade chart with color indicators
            const gradeDistribution = {
                'Poor': 0,
                'Need Improvement': 0,
                'Meet Requirements': 0,
                'Very Good': 0,
                'Excellent': 0
            };

            appraisals.forEach(appraisal => {
                let grade = appraisal.grade;
                // Consolidate legacy grade names
                if (grade === 'Meets Requirements') {
                    grade = 'Meet Requirements';
                }
                if (grade && gradeDistribution.hasOwnProperty(grade)) {
                    gradeDistribution[grade]++;
                }
            });

            // Create pie chart with unified grade colors
            const gradeLabels = Object.keys(gradeDistribution);
            const gradeValues = Object.values(gradeDistribution);
            const gradeColors = gradeLabels.map(grade => {
                switch(grade.toLowerCase()) {
                    case 'poor': return '#dc3545'; // Red
                    case 'need improvement': return '#fd7e14'; // Orange
                    case 'meet requirements': return '#198754'; // Green
                    case 'very good': return '#0d6efd'; // Blue
                    case 'excellent': return '#ffc107'; // Yellow
                    case 'good': return '#0d6efd'; // Blue - treat "Good" as "Very Good" (data inconsistency fix)
                    default: return '#6c757d'; // Gray for unknown
                }
            });

            console.log('Grade distribution found:', gradeDistribution);
            console.log('WARNING: Found inconsistent grade data. Grades found:', gradeLabels);

            // Create modern pie chart with proper sizing and centering
            const gradeCtx = document.getElementById('gradeDistributionChart').getContext('2d');

            // Destroy existing chart if it exists
            if (window.gradeDistributionChart && typeof window.gradeDistributionChart.destroy === 'function') {
                window.gradeDistributionChart.destroy();
            }

            window.gradeDistributionChart = new Chart(gradeCtx, {
                type: 'pie',
                data: {
                    labels: gradeLabels,
                    datasets: [{
                        data: gradeValues,
                        backgroundColor: gradeColors,
                        borderColor: '#fff',
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                usePointStyle: true,
                                padding: 15,
                                font: { size: 12 }
                            }
                        }
                    },
                    layout: {
                        padding: {
                            left: 10,
                            right: 10,
                            top: 10,
                            bottom: 10
                        }
                    }
                }
            });

            // Department performance chart (resizable for 12-15+ departments)
            const departmentPerformance = {};

            appraisals.forEach(appraisal => {
                if (appraisal.employee && appraisal.employee.department && appraisal.total_score) {
                    const department = appraisal.employee.department;

                    if (!departmentPerformance[department]) {
                        departmentPerformance[department] = {
                            total: 0,
                            count: 0
                        };
                    }

                    departmentPerformance[department].total += appraisal.total_score;
                    departmentPerformance[department].count++;
                }
            });

            const departmentLabels = [];
            const departmentScores = [];

            for (const [department, data] of Object.entries(departmentPerformance)) {
                departmentLabels.push(department);
                departmentScores.push(data.total / data.count);
            }

            // Create modern bar chart with rounded edges
            const departmentCanvas = document.getElementById('departmentPerformanceChart');
            if (departmentCanvas) {
                window.departmentChart = new Chart(departmentCanvas, {
                    type: 'bar',
                    data: {
                        labels: departmentLabels,
                        datasets: [{
                            label: 'Score (%)',
                            data: departmentScores,
                            backgroundColor: '#4a90e2',
                            borderColor: '#4a90e2',
                            borderWidth: 1,
                            borderRadius: 8,
                            borderSkipped: false
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Department Performance (Average Total Evaluation Score %)',
                                font: { size: 14, weight: 'bold' }
                            },
                            legend: { display: false }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 100,
                                title: {
                                    display: true,
                                    text: 'Score (%)'
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: 'Department'
                                },
                                ticks: {
                                    maxRotation: 45,
                                    minRotation: 0
                                }
                            }
                        }
                    }
                });
            }





            // Performance Bell Curve
            createPerformanceBellCurve(appraisals);

            // Performance Over Period
            createPerformanceOverPeriod(appraisals);

            // Top 5 Performance KPIs
            createTopKPIsCharts(appraisals);
        }

        // Create Performance Bell Curve
        function createPerformanceBellCurve(appraisals) {
            // Count employees by actual grade (not score ranges)
            const gradeCount = {
                'Poor': 0,
                'Need Improvement': 0,
                'Meet Requirements': 0,
                'Very Good': 0,
                'Excellent': 0
            };

            appraisals.forEach(appraisal => {
                let grade = appraisal.grade;
                // Consolidate legacy grade names
                if (grade === 'Meets Requirements') {
                    grade = 'Meet Requirements';
                }
                if (grade && gradeCount.hasOwnProperty(grade)) {
                    gradeCount[grade]++;
                }
            });

            const labels = Object.keys(gradeCount); // X-axis: Grades
            const data = Object.values(gradeCount); // Y-axis: Number of employees
            const colors = labels.map(grade => {
                switch(grade.toLowerCase()) {
                    case 'poor': return '#dc3545'; // Red
                    case 'need improvement': return '#fd7e14'; // Orange
                    case 'meet requirements': return '#198754'; // Green
                    case 'very good': return '#0d6efd'; // Blue
                    case 'excellent': return '#ffc107'; // Yellow
                    default: return '#6c757d'; // Gray
                }
            });

            const bellCurveCanvas = document.getElementById('performanceBellCurve');
            if (bellCurveCanvas) {
                window.bellCurveChart = new Chart(bellCurveCanvas, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'Number of Employees',
                            data: data,
                            borderColor: '#0d6efd',
                            backgroundColor: 'rgba(13, 110, 253, 0.1)',
                            fill: true,
                            tension: 0.4,
                            pointBackgroundColor: colors,
                            pointBorderColor: colors,
                            pointRadius: 6
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Performance Bell Curve (Number of Employees vs Grades)',
                                font: { size: 14, weight: 'bold' }
                            },
                            legend: { display: false }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'Number of Employees'
                                },
                                ticks: {
                                    stepSize: 1,
                                    callback: function(value) {
                                        return Number.isInteger(value) ? value : '';
                                    }
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: 'Performance Grades'
                                }
                            }
                        }
                    }
                });
            }
        }

        // Create Performance Over Period Chart
        function createPerformanceOverPeriod(appraisals) {
            const periodData = {};

            appraisals.forEach(appraisal => {
                if (appraisal.period && appraisal.period.name && appraisal.total_score) {
                    const periodName = appraisal.period.name;
                    if (!periodData[periodName]) {
                        periodData[periodName] = {
                            total: 0,
                            count: 0
                        };
                    }
                    periodData[periodName].total += appraisal.total_score;
                    periodData[periodName].count += 1;
                }
            });

            const labels = Object.keys(periodData);
            const averageScores = labels.map(period =>
                periodData[period].total / periodData[period].count
            );

            const canvas = document.getElementById('performanceOverPeriod');
            if (canvas) {
                window.periodChart = new Chart(canvas, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'Average Score (%)',
                            data: averageScores,
                            borderColor: '#f39c12',
                            backgroundColor: 'rgba(243, 156, 18, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4,
                            pointBackgroundColor: '#f39c12',
                            pointBorderColor: '#f39c12',
                            pointRadius: 6
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Performance Over Period',
                                font: { size: 14, weight: 'bold' }
                            },
                            legend: { display: false }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 100,
                                title: {
                                    display: true,
                                    text: 'Average Score (%)'
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: 'Period'
                                }
                            }
                        }
                    }
                });
            }
        }

        // Create Top KPIs Charts
        async function createTopKPIsCharts(appraisals) {
            try {
                console.log('Creating Top KPIs charts for', appraisals.length, 'appraisals');

                // Get KPI scores from appraisal details
                const kpiScores = {
                    performance: {},
                    behavioral: {}
                };

                // Fetch appraisal scores to get KPI scores (using denormalized structure)
                for (const appraisal of appraisals) {
                    const { data: details, error } = await supabaseClient
                        .from('appraisal_scores')
                        .select(`
                            score,
                            kpi_name,
                            kpi_category_name
                        `)
                        .eq('appraisal_id', appraisal.id);

                    if (error) {
                        console.error('Error fetching appraisal details for', appraisal.id, ':', error);
                        continue;
                    }

                    if (details && details.length > 0) {
                        console.log('Found', details.length, 'KPI scores for appraisal', appraisal.id);
                        details.forEach(detail => {
                            if (detail.kpi_name && detail.score && detail.kpi_category_name) {
                                const category = detail.kpi_category_name.toLowerCase();
                                const kpiName = detail.kpi_name;

                                if (category === 'performance') {
                                    if (!kpiScores.performance[kpiName]) {
                                        kpiScores.performance[kpiName] = { total: 0, count: 0 };
                                    }
                                    kpiScores.performance[kpiName].total += detail.score;
                                    kpiScores.performance[kpiName].count++;
                                } else if (category === 'behavioral') {
                                    if (!kpiScores.behavioral[kpiName]) {
                                        kpiScores.behavioral[kpiName] = { total: 0, count: 0 };
                                    }
                                    kpiScores.behavioral[kpiName].total += detail.score;
                                    kpiScores.behavioral[kpiName].count++;
                                }
                            }
                        });
                    } else {
                        console.log('No KPI details found for appraisal', appraisal.id);
                    }
                }

                console.log('Performance KPIs found:', Object.keys(kpiScores.performance));
                console.log('Behavioral KPIs found:', Object.keys(kpiScores.behavioral));

                // Calculate averages and get top 5
                const performanceAvgs = Object.entries(kpiScores.performance)
                    .map(([name, data]) => ({ name, avg: data.total / data.count }))
                    .sort((a, b) => b.avg - a.avg)
                    .slice(0, 5);

                const behavioralAvgs = Object.entries(kpiScores.behavioral)
                    .map(([name, data]) => ({ name, avg: data.total / data.count }))
                    .sort((a, b) => b.avg - a.avg)
                    .slice(0, 5);

                console.log('Top Performance KPIs:', performanceAvgs);
                console.log('Top Behavioral KPIs:', behavioralAvgs);

                // If no data found, show placeholder message
                if (performanceAvgs.length === 0 && behavioralAvgs.length === 0) {
                    console.log('No KPI data found - charts will be empty');
                }

                // Create Top 5 Performance KPIs chart with modern styling
                const performanceKPICanvas = document.getElementById('topPerformanceKPIsChart');
                if (performanceKPICanvas) {
                    window.topPerformanceKPIsChart = new Chart(performanceKPICanvas, {
                        type: 'bar',
                        data: {
                            labels: performanceAvgs.map(item => item.name),
                            datasets: [{
                                label: 'Score (0-5)',
                                data: performanceAvgs.map(item => item.avg),
                                backgroundColor: '#f39c12',
                                borderColor: '#f39c12',
                                borderWidth: 1,
                                borderRadius: 8,
                                borderSkipped: false
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                title: {
                                    display: true,
                                    text: 'Top 5 Performance KPIs Scores',
                                    font: { size: 14, weight: 'bold' }
                                },
                                legend: { display: false },
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            return `Score: ${context.raw.toFixed(2)}/5`;
                                        }
                                    }
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    max: 5,
                                    title: {
                                        display: true,
                                        text: 'Score (0-5)'
                                    },
                                    ticks: {
                                        stepSize: 0.5,
                                        callback: function(value) {
                                            return value.toFixed(1);
                                        }
                                    }
                                },
                                x: {
                                    title: {
                                        display: true,
                                        text: 'Performance KPIs'
                                    }
                                }
                            }
                        }
                    });
                }

                // Create Top 5 Behavior KPIs chart with modern styling
                const behaviorKPICanvas = document.getElementById('topBehaviorKPIsChart');
                if (behaviorKPICanvas) {
                    window.topBehavioralKPIsChart = new Chart(behaviorKPICanvas, {
                        type: 'bar',
                        data: {
                            labels: behavioralAvgs.map(item => item.name),
                            datasets: [{
                                label: 'Score (0-5)',
                                data: behavioralAvgs.map(item => item.avg),
                                backgroundColor: '#e74c3c',
                                borderColor: '#e74c3c',
                                borderWidth: 1,
                                borderRadius: 8,
                                borderSkipped: false
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                title: {
                                    display: true,
                                    text: 'Top 5 Behavior KPIs Scores',
                                    font: { size: 14, weight: 'bold' }
                                },
                                legend: { display: false },
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            return `Score: ${context.raw.toFixed(2)}/5`;
                                        }
                                    }
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    max: 5,
                                    title: {
                                        display: true,
                                        text: 'Score (0-5)'
                                    },
                                    ticks: {
                                        stepSize: 0.5,
                                        callback: function(value) {
                                            return value.toFixed(1);
                                        }
                                    }
                                },
                                x: {
                                    title: {
                                        display: true,
                                        text: 'Behavioral KPIs'
                                    }
                                }
                            }
                        }
                    });
                }

            } catch (error) {
                console.error('Error creating top KPIs charts:', error);
            }
        }

        // Fix inconsistent grade data - Comprehensive validation
        async function fixInconsistentGrades() {
            try {
                console.log('Checking for inconsistent grade data...');

                // Find ALL appraisals to validate grades against total_score
                const { data: allAppraisals, error } = await supabaseClient
                    .from('appraisals')
                    .select('id, employee_code, grade, total_score');

                if (error) {
                    console.error('Error checking for inconsistent grades:', error);
                    return;
                }

                if (allAppraisals && allAppraisals.length > 0) {
                    console.log('Validating grades for', allAppraisals.length, 'appraisals...');

                    let fixedCount = 0;

                    // Check each appraisal to ensure grade matches total_score (not performance_score)
                    for (const appraisal of allAppraisals) {
                        const correctGrade = appUtils.getGrade(appraisal.total_score);

                        if (appraisal.grade !== correctGrade) {
                            console.log(`Fixing appraisal ${appraisal.id} for ${appraisal.employee_code}: "${appraisal.grade}" -> "${correctGrade}" (Total Score: ${appraisal.total_score}%)`);

                            const { error: updateError } = await supabaseClient
                                .from('appraisals')
                                .update({ grade: correctGrade })
                                .eq('id', appraisal.id);

                            if (updateError) {
                                console.error('Error updating grade for appraisal', appraisal.id, ':', updateError);
                            } else {
                                fixedCount++;
                            }
                        }
                    }

                    if (fixedCount > 0) {
                        console.log(`Grade validation completed: Fixed ${fixedCount} inconsistent grades based on total evaluation %`);

                        // Reload the dashboard to reflect changes
                        setTimeout(() => {
                            loadDashboardData();
                        }, 1000);
                    } else {
                        console.log('Grade validation completed: All grades are consistent with total evaluation %');
                    }
                } else {
                    console.log('No appraisals found for validation.');
                }
            } catch (error) {
                console.error('Error during grade data cleanup:', error);
            }
        }

        // Delete appraisal function
        async function deleteAppraisal(appraisalId, employeeName, periodName) {
            if (!confirm(`Are you sure you want to delete the appraisal for ${employeeName} (${periodName})?\n\nThis will permanently delete:\n- The appraisal record\n- All KPI scores\n- All comments\n\nThis action cannot be undone and will allow the appraisal to be reassigned.`)) {
                return;
            }

            try {
                console.log('Deleting appraisal:', appraisalId);

                // Delete appraisal (CASCADE will automatically delete appraisal_scores)
                const { error } = await supabaseClient
                    .from('appraisals')
                    .delete()
                    .eq('id', appraisalId);

                if (error) throw error;

                appUtils.showNotification(`Appraisal for ${employeeName} deleted successfully. The employee can now be reassigned for this period.`, 'success');

                // Reload dashboard data to reflect changes
                loadDashboardData();

            } catch (error) {
                console.error('Error deleting appraisal:', error);
                appUtils.showNotification('Error deleting appraisal: ' + error.message, 'error');
            }
        }

        // Load current user name
        async function loadCurrentUserName() {
            try {
                const currentUser = appAuth.getCurrentUser();
                if (currentUser && currentUser.username) {
                    document.getElementById('currentUserName').textContent = currentUser.username;
                } else {
                    document.getElementById('currentUserName').textContent = 'Admin User';
                }
            } catch (error) {
                console.error('Error loading user name:', error);
                document.getElementById('currentUserName').textContent = 'Admin User';
            }
        }

        // Global variable to track sort order
        let sortAscending = true;
        let currentAppraisalsData = [];

        // Sort table by score
        function sortTableByScore() {
            const sortIcon = document.getElementById('sortIcon');

            // Toggle sort order
            sortAscending = !sortAscending;

            // Update icon
            if (sortAscending) {
                sortIcon.className = 'fas fa-sort-up';
            } else {
                sortIcon.className = 'fas fa-sort-down';
            }

            // Sort the data
            const sortedData = [...currentAppraisalsData].sort((a, b) => {
                const scoreA = parseFloat(a.total_score) || 0;
                const scoreB = parseFloat(b.total_score) || 0;

                return sortAscending ? scoreA - scoreB : scoreB - scoreA;
            });

            // Repopulate table with sorted data
            populateAppraisalsTable(sortedData);
        }
        // Navigation button press effects
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.nav-link');

            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    // Remove pressed class from all nav links
                    navLinks.forEach(nav => nav.classList.remove('pressed'));

                    // Add pressed class to clicked link
                    this.classList.add('pressed');
                });
            });
        });
    </script>

    <!-- Create Periods Modal -->
    <div class="modal fade" id="createPeriodsModal" tabindex="-1" aria-labelledby="createPeriodsModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createPeriodsModalLabel">
                        <i class="fas fa-calendar-plus mr-2"></i>Create Appraisal Periods
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="createPeriodsForm">
                        <div class="mb-3">
                            <label for="yearInput" class="form-label">Year *</label>
                            <input type="number" class="form-control" id="yearInput" min="2024" max="2030" required>
                            <div class="form-text">Enter the year for which to create periods (e.g., 2026)</div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Period Types to Create *</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="quarterlyPeriods" checked>
                                <label class="form-check-label" for="quarterlyPeriods">
                                    Quarterly Periods (Q1, Q2, Q3, Q4)
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="semesterPeriods" checked>
                                <label class="form-check-label" for="semesterPeriods">
                                    Semester Periods (Semester 1, Semester 2)
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="annualPeriod" checked>
                                <label class="form-check-label" for="annualPeriod">
                                    Annual Period (Full Year)
                                </label>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle mr-1"></i>
                            <strong>Note:</strong> This will create new periods for the selected year. Existing periods and data will not be affected.
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-success" id="confirmCreatePeriods">
                        <i class="fas fa-calendar-plus mr-1"></i>Create Periods
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>