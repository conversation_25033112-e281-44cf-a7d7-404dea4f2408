/* Main styles for HR Performance Evaluation System */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    
    --body-bg: #f8f9fa;
    --body-color: #212529;
    --card-bg: #ffffff;
    --card-border: #dee2e6;
    --input-bg: #ffffff;
    --input-border: #ced4da;
    --input-color: #212529;
    --table-border: #dee2e6;
    --table-stripe: rgba(0, 0, 0, 0.05);
    --table-hover: rgba(0, 0, 0, 0.075);
    
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-base: 1rem;
    --font-weight-normal: 400;
    --font-weight-bold: 700;
    
    --border-radius: 0.375rem;
    --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --transition: all 0.2s ease-in-out;
}

/* Dark mode variables */
body.dark-mode {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #212529;
    --dark-color: #f8f9fa;
    
    --body-bg: #121212;
    --body-color: #e0e0e0;
    --card-bg: #1e1e1e;
    --card-border: #333333;
    --input-bg: #2d2d2d;
    --input-border: #444444;
    --input-color: #e0e0e0;
    --table-border: #333333;
    --table-stripe: rgba(255, 255, 255, 0.05);
    --table-hover: rgba(255, 255, 255, 0.075);
}

/* Base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: 1.5;
    color: var(--body-color);
    background-color: var(--body-bg);
    transition: var(--transition);
    padding-top: 140px; /* Account for fixed header height */
}

a {
    color: var(--primary-color);
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

/* Container */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Header */
.header {
    background: linear-gradient(135deg, #007bff, #0056b3, #004085);
    color: white;
    padding: 0.75rem 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    width: 100%;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%, rgba(255,255,255,0.1) 100%);
    pointer-events: none;
}

/* New header layout styles */
.header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 0.75rem;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo-icon {
    font-size: 1.5rem;
    color: #ffffff;
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
    margin-right: 12px;
    filter: drop-shadow(0 2px 4px rgba(255, 255, 255, 0.3));
}

.logo-text {
    font-size: 1.5rem;
    font-weight: bold;
    color: #ffffff;
    text-shadow: 0 0 6px rgba(0, 0, 0, 0.5), 0 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.8px;
    line-height: 1.2;
}

/* Dark mode logo adjustments */
body.dark-mode .logo-text {
    color: #ffffff;
    text-shadow: 0 0 10px rgba(0, 0, 0, 0.7), 0 2px 6px rgba(0, 0, 0, 0.5);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-name {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

.logout-btn {
    color: white;
    text-decoration: none;
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

.nav-bottom {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.nav-bottom .nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.logo {
    font-size: 1.25rem;
    font-weight: var(--font-weight-bold);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.nav {
    display: flex;
    gap: 0.5rem;
}

.nav-link {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    transition: all 0.3s ease;
    font-weight: 500;
    position: relative;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    text-decoration: none;
    color: white;
}

.nav-link.active {
    background: rgba(255, 255, 255, 0.25);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.4);
}

/* Enhanced button press effects - Blue theme matching */
.nav-link:active,
.nav-link.pressed {
    background: linear-gradient(135deg, #2c5aa0 0%, #1e3a8a 100%);
    transform: translateY(1px) scale(0.98);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.6);
    transition: all 0.15s ease;
}

/* Smooth transitions for all nav-link states */
.nav-link {
    transition: all 0.2s ease;
}

/* Current page indicator - PERSISTENT blue pressed effect */
.nav-link.current-page {
    background: linear-gradient(135deg, #2c5aa0 0%, #1e3a8a 100%) !important;
    transform: translateY(1px) scale(0.98) !important;
    box-shadow: 0 3px 8px rgba(44, 90, 160, 0.5) !important;
    border-color: rgba(255, 255, 255, 0.7) !important;
    font-weight: 600;
    /* This effect stays persistent to show current page */
}

/* Main content */
.main {
    padding: 2rem 0;
}

/* Card */
.card {
    background-color: var(--card-bg);
    border: 1px solid var(--card-border);
    border-radius: var(--border-radius);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
    transition: var(--transition);
}

.card-header {
    padding: 1rem;
    border-bottom: 1px solid var(--card-border);
    font-weight: var(--font-weight-bold);
    background-color: rgba(0, 0, 0, 0.03);
    color: var(--body-color);
}

body.dark-mode .card-header {
    background-color: rgba(255, 255, 255, 0.05);
    color: var(--body-color);
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    padding: 1rem;
    border-top: 1px solid var(--card-border);
    background-color: rgba(0, 0, 0, 0.03);
}

/* Forms */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: var(--font-weight-bold);
}

.form-control {
    display: block;
    width: 100%;
    padding: 0.375rem 0.75rem;
    font-size: var(--font-size-base);
    line-height: 1.5;
    color: var(--input-color);
    background-color: var(--input-bg);
    border: 1px solid var(--input-border);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-control.invalid {
    border-color: var(--danger-color);
}

.form-select {
    display: block;
    width: 100%;
    padding: 0.375rem 2.25rem 0.375rem 0.75rem;
    font-size: var(--font-size-base);
    line-height: 1.5;
    color: var(--input-color);
    background-color: var(--input-bg);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    border: 1px solid var(--input-border);
    border-radius: var(--border-radius);
    appearance: none;
    transition: var(--transition);
}

.form-select:focus {
    border-color: var(--primary-color);
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Buttons */
.btn {
    display: inline-block;
    font-weight: 400;
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    padding: 0.375rem 0.75rem;
    font-size: var(--font-size-base);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.btn-primary {
    color: #fff;
    background-color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

.btn-secondary {
    color: #fff;
    background-color: var(--secondary-color);
    border: 1px solid var(--secondary-color);
}

.btn-secondary:hover {
    background-color: #5c636a;
    border-color: #565e64;
}

.btn-success {
    color: #fff;
    background-color: var(--success-color);
    border: 1px solid var(--success-color);
}

.btn-success:hover {
    background-color: #157347;
    border-color: #146c43;
}

.btn-danger {
    color: #fff;
    background-color: var(--danger-color);
    border: 1px solid var(--danger-color);
}

.btn-danger:hover {
    background-color: #bb2d3b;
    border-color: #b02a37;
}

.btn-warning {
    color: #000;
    background-color: var(--warning-color);
    border: 1px solid var(--warning-color);
}

.btn-warning:hover {
    background-color: #ffca2c;
    border-color: #ffc720;
}

.btn-info {
    color: #000;
    background-color: var(--info-color);
    border: 1px solid var(--info-color);
}

.btn-info:hover {
    background-color: #31d2f2;
    border-color: #25cff2;
}

.btn-light {
    color: #000;
    background-color: var(--light-color);
    border: 1px solid var(--light-color);
}

.btn-light:hover {
    background-color: #f9fafb;
    border-color: #f9fafb;
}

.btn-dark {
    color: #fff;
    background-color: var(--dark-color);
    border: 1px solid var(--dark-color);
}

.btn-dark:hover {
    background-color: #1a1e21;
    border-color: #191c1f;
}

.btn-outline-primary {
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
    background-color: transparent;
}

.btn-outline-primary:hover {
    color: #fff;
    background-color: var(--primary-color);
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 0.25rem;
}

.btn-lg {
    padding: 0.5rem 1rem;
    font-size: 1.25rem;
    border-radius: 0.5rem;
}

/* Tables */
.table {
    width: 100%;
    margin-bottom: 1rem;
    color: var(--body-color);
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 0.75rem;
    vertical-align: top;
    border-top: 1px solid var(--table-border);
}

.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid var(--table-border);
    background-color: rgba(0, 0, 0, 0.03);
    color: var(--body-color);
}

body.dark-mode .table thead th {
    background-color: rgba(255, 255, 255, 0.05);
    color: var(--body-color);
}

/* Additional dark mode table text fixes */
body.dark-mode .table {
    color: #e0e0e0;
}

body.dark-mode .table td {
    color: #e0e0e0;
}

body.dark-mode .table tbody tr {
    color: #e0e0e0;
}

body.dark-mode .table th {
    color: #e0e0e0;
}

.table tbody + tbody {
    border-top: 2px solid var(--table-border);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: var(--table-stripe);
}

.table-hover tbody tr:hover {
    background-color: var(--table-hover);
}

.table-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

/* Badges */
.badge {
    display: inline-block;
    padding: 0.35em 0.65em;
    font-size: 0.75em;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
}

.badge-primary {
    color: #fff;
    background-color: var(--primary-color);
}

.badge-secondary {
    color: #fff;
    background-color: var(--secondary-color);
}

.badge-success {
    color: #fff;
    background-color: var(--success-color);
}

.badge-danger {
    color: #fff;
    background-color: var(--danger-color);
}

.badge-warning {
    color: #000;
    background-color: var(--warning-color);
}

.badge-info {
    color: #000;
    background-color: var(--info-color);
}

/* Alerts */
.alert {
    position: relative;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
}

.alert-primary {
    color: #084298;
    background-color: #cfe2ff;
    border-color: #b6d4fe;
}

.alert-secondary {
    color: #41464b;
    background-color: #e2e3e5;
    border-color: #d3d6d8;
}

.alert-success {
    color: #0f5132;
    background-color: #d1e7dd;
    border-color: #badbcc;
}

.alert-danger {
    color: #842029;
    background-color: #f8d7da;
    border-color: #f5c2c7;
}

.alert-warning {
    color: #664d03;
    background-color: #fff3cd;
    border-color: #ffecb5;
}

.alert-info {
    color: #055160;
    background-color: #cff4fc;
    border-color: #b6effb;
}

/* Utilities */
.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.text-left {
    text-align: left;
}

.mt-1 {
    margin-top: 0.25rem;
}

.mt-2 {
    margin-top: 0.5rem;
}

.mt-3 {
    margin-top: 1rem;
}

.mt-4 {
    margin-top: 1.5rem;
}

.mt-5 {
    margin-top: 3rem;
}

.mb-1 {
    margin-bottom: 0.25rem;
}

.mb-2 {
    margin-bottom: 0.5rem;
}

.mb-3 {
    margin-bottom: 1rem;
}

.mb-4 {
    margin-bottom: 1.5rem;
}

.mb-5 {
    margin-bottom: 3rem;
}

.ml-1 {
    margin-left: 0.25rem;
}

.ml-2 {
    margin-left: 0.5rem;
}

.ml-3 {
    margin-left: 1rem;
}

.mr-1 {
    margin-right: 0.25rem;
}

.mr-2 {
    margin-right: 0.5rem;
}

.mr-3 {
    margin-right: 1rem;
}

.p-1 {
    padding: 0.25rem;
}

.p-2 {
    padding: 0.5rem;
}

.p-3 {
    padding: 1rem;
}

.p-4 {
    padding: 1.5rem;
}

.p-5 {
    padding: 3rem;
}

.d-flex {
    display: flex;
}

.flex-column {
    flex-direction: column;
}

.justify-content-between {
    justify-content: space-between;
}

.justify-content-center {
    justify-content: center;
}

.align-items-center {
    align-items: center;
}

.w-100 {
    width: 100%;
}

.h-100 {
    height: 100%;
}

/* Grid system */
.row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
}

.col {
    flex: 1 0 0%;
    padding-right: 15px;
    padding-left: 15px;
}

.col-auto {
    flex: 0 0 auto;
    width: auto;
    padding-right: 15px;
    padding-left: 15px;
}

.col-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
    padding-right: 15px;
    padding-left: 15px;
}

.col-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
    padding-right: 15px;
    padding-left: 15px;
}

.col-3 {
    flex: 0 0 auto;
    width: 25%;
    padding-right: 15px;
    padding-left: 15px;
}

.col-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
    padding-right: 15px;
    padding-left: 15px;
}

.col-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
    padding-right: 15px;
    padding-left: 15px;
}

.col-6 {
    flex: 0 0 auto;
    width: 50%;
    padding-right: 15px;
    padding-left: 15px;
}

.col-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
    padding-right: 15px;
    padding-left: 15px;
}

.col-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
    padding-right: 15px;
    padding-left: 15px;
}

.col-9 {
    flex: 0 0 auto;
    width: 75%;
    padding-right: 15px;
    padding-left: 15px;
}

.col-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
    padding-right: 15px;
    padding-left: 15px;
}

.col-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
    padding-right: 15px;
    padding-left: 15px;
}

.col-12 {
    flex: 0 0 auto;
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
}

/* Responsive utilities */
@media (min-width: 576px) {
    .container {
        max-width: 540px;
    }
    
    .col-sm-1 {
        flex: 0 0 auto;
        width: 8.33333333%;
    }
    
    .col-sm-2 {
        flex: 0 0 auto;
        width: 16.66666667%;
    }
    
    .col-sm-3 {
        flex: 0 0 auto;
        width: 25%;
    }
    
    .col-sm-4 {
        flex: 0 0 auto;
        width: 33.33333333%;
    }
    
    .col-sm-5 {
        flex: 0 0 auto;
        width: 41.66666667%;
    }
    
    .col-sm-6 {
        flex: 0 0 auto;
        width: 50%;
    }
    
    .col-sm-7 {
        flex: 0 0 auto;
        width: 58.33333333%;
    }
    
    .col-sm-8 {
        flex: 0 0 auto;
        width: 66.66666667%;
    }
    
    .col-sm-9 {
        flex: 0 0 auto;
        width: 75%;
    }
    
    .col-sm-10 {
        flex: 0 0 auto;
        width: 83.33333333%;
    }
    
    .col-sm-11 {
        flex: 0 0 auto;
        width: 91.66666667%;
    }
    
    .col-sm-12 {
        flex: 0 0 auto;
        width: 100%;
    }
}

@media (min-width: 768px) {
    .container {
        max-width: 720px;
    }
    
    .col-md-1 {
        flex: 0 0 auto;
        width: 8.33333333%;
    }
    
    .col-md-2 {
        flex: 0 0 auto;
        width: 16.66666667%;
    }
    
    .col-md-3 {
        flex: 0 0 auto;
        width: 25%;
    }
    
    .col-md-4 {
        flex: 0 0 auto;
        width: 33.33333333%;
    }
    
    .col-md-5 {
        flex: 0 0 auto;
        width: 41.66666667%;
    }
    
    .col-md-6 {
        flex: 0 0 auto;
        width: 50%;
    }
    
    .col-md-7 {
        flex: 0 0 auto;
        width: 58.33333333%;
    }
    
    .col-md-8 {
        flex: 0 0 auto;
        width: 66.66666667%;
    }
    
    .col-md-9 {
        flex: 0 0 auto;
        width: 75%;
    }
    
    .col-md-10 {
        flex: 0 0 auto;
        width: 83.33333333%;
    }
    
    .col-md-11 {
        flex: 0 0 auto;
        width: 91.66666667%;
    }
    
    .col-md-12 {
        flex: 0 0 auto;
        width: 100%;
    }
}

@media (min-width: 992px) {
    .container {
        max-width: 960px;
    }
    
    .col-lg-1 {
        flex: 0 0 auto;
        width: 8.33333333%;
    }
    
    .col-lg-2 {
        flex: 0 0 auto;
        width: 16.66666667%;
    }
    
    .col-lg-3 {
        flex: 0 0 auto;
        width: 25%;
    }
    
    .col-lg-4 {
        flex: 0 0 auto;
        width: 33.33333333%;
    }
    
    .col-lg-5 {
        flex: 0 0 auto;
        width: 41.66666667%;
    }
    
    .col-lg-6 {
        flex: 0 0 auto;
        width: 50%;
    }
    
    .col-lg-7 {
        flex: 0 0 auto;
        width: 58.33333333%;
    }
    
    .col-lg-8 {
        flex: 0 0 auto;
        width: 66.66666667%;
    }
    
    .col-lg-9 {
        flex: 0 0 auto;
        width: 75%;
    }
    
    .col-lg-10 {
        flex: 0 0 auto;
        width: 83.33333333%;
    }
    
    .col-lg-11 {
        flex: 0 0 auto;
        width: 91.66666667%;
    }
    
    .col-lg-12 {
        flex: 0 0 auto;
        width: 100%;
    }
}

@media (min-width: 1200px) {
    .container {
        max-width: 1140px;
    }
    
    .col-xl-1 {
        flex: 0 0 auto;
        width: 8.33333333%;
    }
    
    .col-xl-2 {
        flex: 0 0 auto;
        width: 16.66666667%;
    }
    
    .col-xl-3 {
        flex: 0 0 auto;
        width: 25%;
    }
    
    .col-xl-4 {
        flex: 0 0 auto;
        width: 33.33333333%;
    }
    
    .col-xl-5 {
        flex: 0 0 auto;
        width: 41.66666667%;
    }
    
    .col-xl-6 {
        flex: 0 0 auto;
        width: 50%;
    }
    
    .col-xl-7 {
        flex: 0 0 auto;
        width: 58.33333333%;
    }
    
    .col-xl-8 {
        flex: 0 0 auto;
        width: 66.66666667%;
    }
    
    .col-xl-9 {
        flex: 0 0 auto;
        width: 75%;
    }
    
    .col-xl-10 {
        flex: 0 0 auto;
        width: 83.33333333%;
    }
    
    .col-xl-11 {
        flex: 0 0 auto;
        width: 91.66666667%;
    }
    
    .col-xl-12 {
        flex: 0 0 auto;
        width: 100%;
    }
}

/* Custom components */

/* Login form */
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: var(--body-bg);
}

.login-card {
    width: 100%;
    max-width: 400px;
    padding: 2rem;
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.login-logo {
    text-align: center;
    margin-bottom: 2rem;
}

.login-title {
    text-align: center;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    font-weight: var(--font-weight-bold);
}

/* Dashboard */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.stat-title {
    font-size: 1rem;
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
}

.stat-value {
    font-size: 2rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: 0.5rem;
}

.stat-icon {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

/* Charts container */
.charts-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.chart-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.chart-title {
    font-size: 1.25rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: 1rem;
}

/* Appraisal form */
.appraisal-form {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.appraisal-header {
    margin-bottom: 2rem;
}

.appraisal-section {
    margin-bottom: 2rem;
}

.appraisal-section-title {
    font-size: 1.25rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--card-border);
}

.kpi-item {
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px dashed var(--card-border);
}

.kpi-item:last-child {
    border-bottom: none;
}

.kpi-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.kpi-name {
    font-weight: var(--font-weight-bold);
}

.kpi-weight {
    color: var(--secondary-color);
}

.kpi-description {
    margin-bottom: 1rem;
    color: var(--secondary-color);
}

.score-options {
    display: flex;
    gap: 0.5rem;
}

.score-option {
    flex: 1;
    text-align: center;
}

.score-radio {
    display: none;
}

.score-label {
    display: block;
    padding: 0.5rem;
    border: 1px solid var(--card-border);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.score-radio:checked + .score-label {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.score-value {
    font-weight: var(--font-weight-bold);
    font-size: 1.25rem;
}

.score-text {
    font-size: 0.875rem;
}

/* Appraisal report */
.appraisal-report {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.report-header {
    margin-bottom: 2rem;
}

.report-title {
    font-size: 1.5rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: 0.5rem;
}

.report-subtitle {
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

.report-info {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.info-item {
    margin-bottom: 0.5rem;
}

.info-label {
    font-weight: var(--font-weight-bold);
    margin-bottom: 0.25rem;
}

.report-section {
    margin-bottom: 2rem;
}

.report-section-title {
    font-size: 1.25rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--card-border);
}

.gauge-container {
    display: flex;
    justify-content: center;
    margin: 2rem 0;
}

.score-summary {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: rgba(0, 0, 0, 0.03);
    border-radius: var(--border-radius);
}

.summary-label {
    font-weight: var(--font-weight-bold);
}

.summary-value {
    font-weight: var(--font-weight-bold);
}

.grade-excellent {
    color: var(--success-color);
}

.grade-very-good {
    color: #20c997;
}

.grade-meets-requirements {
    color: var(--warning-color);
}

.grade-need-improvement {
    color: #fd7e14;
}

.grade-poor {
    color: var(--danger-color);
}

/* Notification */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem;
    border-radius: var(--border-radius);
    background-color: var(--success-color);
    color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transform: translateX(120%);
    transition: transform 0.3s ease-in-out;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background-color: var(--success-color);
}

.notification.error {
    background-color: var(--danger-color);
}

.notification.warning {
    background-color: var(--warning-color);
    color: #000;
}

.notification.info {
    background-color: var(--info-color);
    color: #000;
}

/* Dark mode toggle */
.dark-mode-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transition: var(--transition);
}

.dark-mode-toggle:hover {
    transform: scale(1.1);
}

/* Footer */
.footer {
    background-color: var(--card-bg);
    padding: 1.5rem 0;
    text-align: center;
    margin-top: 2rem;
    border-top: 1px solid var(--card-border);
}

.footer-text {
    color: var(--secondary-color);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .header-container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
    
    .dashboard-stats {
        grid-template-columns: 1fr;
    }
    
    .charts-container {
        grid-template-columns: 1fr;
    }
    
    .score-options {
        flex-wrap: wrap;
    }
    
    .score-option {
        flex: 0 0 calc(20% - 0.5rem);
    }
    
    .report-info {
        grid-template-columns: 1fr;
    }

    .modal-dialog {
        margin: 1rem;
        max-width: calc(100% - 2rem);
    }
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1050;
    display: none;
}

.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1040;
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: 1.75rem auto;
    max-width: 500px;
    z-index: 1060;
}

.modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    background-color: var(--card-bg);
    border: 1px solid var(--card-border);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid var(--card-border);
}

.modal-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: var(--font-weight-bold);
    color: var(--body-color);
}

.modal-body {
    position: relative;
    flex: 1 1 auto;
    padding: 1rem;
}

.modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 0.5rem;
    padding: 1rem;
    border-top: 1px solid var(--card-border);
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    line-height: 1;
    color: var(--secondary-color);
    cursor: pointer;
    padding: 0;
    width: 1.5rem;
    height: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-modal:hover {
    color: var(--danger-color);
}

/* Performance Grade Color System */
.grade-poor, .grade-1 {
    color: #dc3545 !important; /* Red */
    background-color: rgba(220, 53, 69, 0.1) !important;
}

.grade-needs-improvement, .grade-2 {
    color: #fd7e14 !important; /* Orange */
    background-color: rgba(253, 126, 20, 0.1) !important;
}

.grade-meets-requirements, .grade-3 {
    color: #198754 !important; /* Green */
    background-color: rgba(25, 135, 84, 0.1) !important;
}

.grade-very-good, .grade-4 {
    color: #0d6efd !important; /* Blue */
    background-color: rgba(13, 110, 253, 0.1) !important;
}

.grade-excellent, .grade-5 {
    color: #ffc107 !important; /* Yellow */
    background-color: rgba(255, 193, 7, 0.1) !important;
}

/* Grade badges */
.grade-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-weight: 600;
    font-size: 0.875rem;
    display: inline-block;
}

.grade-badge.grade-poor { background-color: #dc3545; color: white; }
.grade-badge.grade-needs-improvement { background-color: #fd7e14; color: white; }
.grade-badge.grade-meets-requirements { background-color: #198754; color: white; }
.grade-badge.grade-very-good { background-color: #0d6efd; color: white; }
.grade-badge.grade-excellent { background-color: #ffc107; color: #000; }

/* Chart color variables */
:root {
    --grade-poor-color: #dc3545;
    --grade-needs-improvement-color: #fd7e14;
    --grade-meets-requirements-color: #198754;
    --grade-very-good-color: #0d6efd;
    --grade-excellent-color: #ffc107;
}