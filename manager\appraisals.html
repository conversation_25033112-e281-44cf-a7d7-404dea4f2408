<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Appraisals - HR Performance Evaluation System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    
    <!-- SheetJS (XLSX) -->
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/styles.css">

    <style>
        /* Modern color palette for charts */
        :root {
            --chart-primary: #4a90e2;
            --chart-secondary: #7bb3f0;
            --chart-success: #27ae60;
            --chart-info: #3498db;
            --chart-warning: #f39c12;
            --chart-danger: #e74c3c;
            --chart-purple: #9b59b6;
            --chart-orange: #e67e22;
            --chart-teal: #1abc9c;
            --chart-pink: #e91e63;
        }

        /* Interactive filter button styles */
        .filter-btn-active {
            background-color: #e74c3c !important;
            border-color: #e74c3c !important;
            color: white !important;
        }

        /* Modern card styling */
        .card {
            border-radius: 8px;
            border: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 15px;
        }

        .card-header {
            background: linear-gradient(135deg, #4a90e2 0%, #7bb3f0 100%);
            color: white;
            border-radius: 8px 8px 0 0;
            border: none;
            padding: 10px 15px;
        }

        .card-body {
            padding: 10px;
        }

        /* Filter section styling */
        .filter-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        /* Navigation button press effects */
        .nav-link {
            transition: all 0.3s ease;
            transform: scale(1);
        }

        .nav-link.pressed {
            transform: scale(0.9);
            background-color: #0056b3 !important;
            color: white !important;
        }

        .nav-link.active {
            transform: scale(0.9);
            background-color: #0056b3 !important;
            color: white !important;
        }

        /* Dark mode support */
        body.dark-mode .filter-section {
            background: #2d2d2d;
            border: 1px solid #444444;
            color: #e0e0e0;
        }

        body.dark-mode .card {
            background-color: #1e1e1e;
            border-color: #333333;
            color: #e0e0e0;
        }

        body.dark-mode .card-header {
            background-color: rgba(255, 255, 255, 0.05);
            border-color: #333333;
            color: #e0e0e0;
        }

        body.dark-mode .table {
            color: #e0e0e0;
        }

        body.dark-mode .table thead th {
            background-color: rgba(255, 255, 255, 0.05);
            color: #e0e0e0;
        }

        body.dark-mode .form-label {
            color: #e0e0e0;
        }

        body.dark-mode h1, body.dark-mode h2, body.dark-mode h3 {
            color: #e0e0e0;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-top">
                <div class="logo-section">
                    <i class="fas fa-chart-line logo-icon"></i>
                    <span class="logo-text">Performance Evaluation System</span>
                </div>
                <div class="user-info">
                    <span id="currentUserName" class="user-name">Loading...</span>
                    <a href="#" id="logoutBtn" class="logout-btn">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
            </div>
            <nav class="nav-bottom">
                <a href="team.html" class="nav-link">
                    <i class="fas fa-users"></i> My Team
                </a>
                <a href="appraisals.html" class="nav-link active">
                    <i class="fas fa-clipboard-check"></i> Appraisals
                </a>
                <a href="reports.html" class="nav-link">
                    <i class="fas fa-chart-bar"></i> Reports
                </a>
                <a href="my-appraisal.html" class="nav-link">
                    <i class="fas fa-user-check"></i> My Appraisal
                </a>
            </nav>
        </div>
    </header>
    
    <!-- Main content -->
    <main class="main">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="text-2xl font-bold">Appraisals</h1>
                <div>
                    <button id="exportAppraisalsBtn" class="btn btn-success">
                        <i class="fas fa-download mr-1"></i> Export to Excel
                    </button>
                </div>
            </div>
            
            <!-- Filters -->
            <div class="card mb-3 filter-section">
                <div class="card-body py-2">
                    <div class="row g-2">
                        <div class="col-md-2">
                            <label for="teamScopeFilter" class="form-label small">Team Scope</label>
                            <select id="teamScopeFilter" class="form-select form-select-sm">
                                <option value="direct">My Direct Team</option>
                                <option value="extended">My Extended Team</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="employeeFilter" class="form-label small">Employee</label>
                            <select id="employeeFilter" class="form-select form-select-sm">
                                <option value="">All Employees</option>
                                <!-- Employees will be loaded dynamically -->
                            </select>
                        </div>
                        <div class="col-md-1">
                            <label for="yearFilter" class="form-label small">Year</label>
                            <select id="yearFilter" class="form-select form-select-sm">
                                <option value="">All Years</option>
                                <!-- Years will be loaded dynamically -->
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="periodTypeFilter" class="form-label small">Period Type</label>
                            <select id="periodTypeFilter" class="form-select form-select-sm">
                                <option value="">All Periods</option>
                                <option value="Q1">Q1</option>
                                <option value="Q2">Q2</option>
                                <option value="Q3">Q3</option>
                                <option value="Q4">Q4</option>
                                <option value="Semester 1">Semester 1</option>
                                <option value="Semester 2">Semester 2</option>
                                <option value="Annual">Annual</option>
                            </select>
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button id="applyFilters" class="btn btn-primary btn-sm me-2">
                                <i class="fas fa-filter"></i> Apply
                            </button>
                            <button id="resetFilters" class="btn btn-secondary btn-sm">
                                <i class="fas fa-times"></i> Reset
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Appraisals Table -->
            <div class="card">
                <div class="card-header py-2">
                    <h5 class="mb-0">Appraisal History</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="appraisalsTable" class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Employee</th>
                                    <th>Period</th>
                                    <th>Performance Score</th>
                                    <th>Behavioral Score</th>
                                    <th>Total Score</th>
                                    <th>Grade</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="appraisalsTableBody">
                                <!-- Appraisals will be loaded dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p class="footer-text">© Produced by: Dr. Ahmed Atef - All rights reserved.</p>
        </div>
    </footer>
    
    <!-- Dark mode toggle -->
    <div id="darkModeToggle" class="dark-mode-toggle" title="Switch to Dark Mode">
        <i class="fas fa-moon"></i>
    </div>
    
    <!-- Supabase JS -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2/dist/umd/supabase.js"></script>
    
    <!-- Custom JS -->
    <script src="../assets/js/utils.js"></script>
    <script src="../assets/js/supabase.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication and role
            if (!appAuth.checkAuth('manager')) {
                return;
            }

            // User name will be loaded with manager data
            
            // Apply dark mode if previously enabled
            appUtils.applyDarkMode();
            
            // Dark mode toggle
            document.getElementById('darkModeToggle').addEventListener('click', function() {
                appUtils.toggleDarkMode();
            });
            
            // Logout button
            document.getElementById('logoutBtn').addEventListener('click', function(e) {
                e.preventDefault();
                appAuth.logoutUser();
            });
            
            // Load filters
            loadFilters();
            
            // Load appraisals
            loadAppraisals();
            
            // Apply filters button
            document.getElementById('applyFilters').addEventListener('click', function() {
                // Add active state to apply button
                this.classList.add('filter-btn-active');
                document.getElementById('resetFilters').classList.remove('filter-btn-active');
                loadAppraisals();
            });

            // Team scope filter change
            document.getElementById('teamScopeFilter').addEventListener('change', async function() {
                const currentManager = appAuth.getCurrentEmployee();
                if (currentManager) {
                    await loadEmployeeFilter(currentManager.code_number);
                    loadAppraisals();
                }
            });

            // Reset filters button
            document.getElementById('resetFilters').addEventListener('click', function() {
                // Reset filter values
                document.getElementById('teamScopeFilter').value = 'direct';
                document.getElementById('employeeFilter').value = '';
                document.getElementById('yearFilter').value = '';
                document.getElementById('periodTypeFilter').value = '';

                // Remove active state from apply button and restore normal state
                document.getElementById('applyFilters').classList.remove('filter-btn-active');
                this.classList.remove('filter-btn-active');

                // Reload employee filter and appraisals
                const currentManager = appAuth.getCurrentEmployee();
                if (currentManager) {
                    loadEmployeeFilter(currentManager.code_number);
                }
                loadAppraisals();
            });
            
            // Export to Excel button
            document.getElementById('exportAppraisalsBtn').addEventListener('click', function() {
                appUtils.exportToExcel('appraisalsTable', 'Appraisals_History');
            });
        });

        // Get all team members in the hierarchy (recursive)
        async function getAllTeamMembers(managerCode) {
            try {
                const allMembers = [];

                // Get direct reports
                const { data: directReports, error } = await supabaseClient
                    .from('employees')
                    .select('*')
                    .eq('manager_code', managerCode);

                if (error) throw error;

                if (directReports && directReports.length > 0) {
                    allMembers.push(...directReports);

                    // Recursively get reports of reports
                    for (const employee of directReports) {
                        const subTeam = await getAllTeamMembers(employee.code_number);
                        allMembers.push(...subTeam);
                    }
                }

                return allMembers;
            } catch (error) {
                console.error('Error getting team members:', error);
                return [];
            }
        }

        // Load filter options
        async function loadFilters() {
            try {
                // Get current manager
                const currentManager = appAuth.getCurrentEmployee();
                if (!currentManager) return;

                // Update user name display
                document.getElementById('currentUserName').textContent = currentManager.name;
                
                // Load employees filter based on team scope
                await loadEmployeeFilter(currentManager.code_number);
                
                // Load periods
                const { data: periods } = await supabaseClient
                    .from('appraisal_periods')
                    .select('id, name')
                    .order('start_date', { ascending: false });
                
                if (periods) {
                    const periodFilter = document.getElementById('periodFilter');
                    
                    periods.forEach(period => {
                        const option = document.createElement('option');
                        option.value = period.id;
                        option.textContent = period.name;
                        periodFilter.appendChild(option);
                    });
                }
                
            } catch (error) {
                console.error('Error loading filters:', error);
                appUtils.showNotification('Error loading filters', 'error');
            }
        }

        // Load employee filter based on team scope
        async function loadEmployeeFilter(managerCode) {
            try {
                const teamScope = document.getElementById('teamScopeFilter').value;
                let teamMembers;

                if (teamScope === 'extended') {
                    // Get all team members in hierarchy
                    teamMembers = await getAllTeamMembers(managerCode);
                } else {
                    // Get only direct reports
                    const { data: directReports } = await supabaseClient
                        .from('employees')
                        .select('code_number, name')
                        .eq('manager_code', managerCode)
                        .order('name');
                    teamMembers = directReports || [];
                }

                // Clear and populate employee filter
                const employeeFilter = document.getElementById('employeeFilter');
                employeeFilter.innerHTML = '<option value="">All Employees</option>';

                teamMembers.forEach(employee => {
                    const option = document.createElement('option');
                    option.value = employee.code_number;
                    option.textContent = employee.name;
                    employeeFilter.appendChild(option);
                });

            } catch (error) {
                console.error('Error loading employee filter:', error);
            }
        }

        // Load appraisals
        async function loadAppraisals() {
            try {
                // Get current manager
                const currentManager = appAuth.getCurrentEmployee();
                if (!currentManager) return;
                
                // Get filter values
                const teamScope = document.getElementById('teamScopeFilter').value;
                const employeeFilter = document.getElementById('employeeFilter').value;
                const periodFilter = document.getElementById('periodFilter').value;

                // Get team members based on scope
                let teamMembers;
                if (teamScope === 'extended') {
                    teamMembers = await getAllTeamMembers(currentManager.code_number);
                } else {
                    const { data: directReports } = await supabaseClient
                        .from('employees')
                        .select('code_number')
                        .eq('manager_code', currentManager.code_number);
                    teamMembers = directReports || [];
                }

                const teamMemberCodes = teamMembers.map(emp => emp.code_number);

                // Build query based on team scope
                let query = supabaseClient
                    .from('appraisals')
                    .select(`
                        *,
                        employee:employees!appraisals_employee_code_fkey(code_number, name),
                        manager:employees!appraisals_manager_code_fkey(code_number, name),
                        period:appraisal_periods(id, name)
                    `)
                    .in('employee_code', teamMemberCodes);
                
                if (employeeFilter) {
                    query = query.eq('employee_code', employeeFilter);
                }
                
                if (periodFilter) {
                    query = query.eq('period_id', periodFilter);
                }
                
                // Execute query
                const { data: appraisals, error } = await query.order('created_at', { ascending: false });
                
                if (error) throw error;
                
                // Populate table
                populateAppraisalsTable(appraisals || []);
                
            } catch (error) {
                console.error('Error loading appraisals:', error);
                appUtils.showNotification('Error loading appraisals', 'error');
            }
        }
        
        // Populate appraisals table
        function populateAppraisalsTable(appraisals) {
            const tableBody = document.getElementById('appraisalsTableBody');
            tableBody.innerHTML = '';
            
            if (appraisals.length === 0) {
                const row = document.createElement('tr');
                const cell = document.createElement('td');
                cell.colSpan = 8;
                cell.textContent = 'No appraisals found';
                cell.className = 'text-center';
                row.appendChild(cell);
                tableBody.appendChild(row);
                return;
            }
            
            appraisals.forEach(appraisal => {
                const row = document.createElement('tr');
                
                // Employee
                const employeeCell = document.createElement('td');
                employeeCell.textContent = appraisal.employee ? appraisal.employee.name : 'Unknown';
                row.appendChild(employeeCell);
                
                // Period
                const periodCell = document.createElement('td');
                periodCell.textContent = appraisal.period ? appraisal.period.name : 'Unknown';
                row.appendChild(periodCell);
                
                // Performance Score
                const performanceCell = document.createElement('td');
                performanceCell.textContent = appraisal.performance_score ? appraisal.performance_score.toFixed(1) : 'N/A';
                row.appendChild(performanceCell);
                
                // Behavioral Score
                const behavioralCell = document.createElement('td');
                behavioralCell.textContent = appraisal.behavioral_score ? appraisal.behavioral_score.toFixed(1) : 'N/A';
                row.appendChild(behavioralCell);
                
                // Total Score
                const totalScoreCell = document.createElement('td');
                totalScoreCell.textContent = appraisal.total_score ? appraisal.total_score.toFixed(1) : 'N/A';
                row.appendChild(totalScoreCell);
                
                // Grade
                const gradeCell = document.createElement('td');
                if (appraisal.grade) {
                    gradeCell.textContent = appraisal.grade;
                    gradeCell.style.color = appUtils.getGradeColor(appraisal.grade);
                } else {
                    gradeCell.textContent = 'N/A';
                }
                row.appendChild(gradeCell);
                
                // Status - NEW WORKFLOW
                const statusCell = document.createElement('td');
                let status = 'Draft';
                let statusClass = 'badge-warning';

                if (appraisal.manager_signature && appraisal.employee_signature) {
                    status = 'Completed';
                    statusClass = 'badge-success';
                } else if (appraisal.manager_signature && appraisal.employee_signature_requested) {
                    status = 'Pending Employee';
                    statusClass = 'badge-info';
                } else if (appraisal.manager_signature) {
                    status = 'Submitted';
                    statusClass = 'badge-primary';
                }

                statusCell.innerHTML = `<span class="badge ${statusClass}">${status}</span>`;
                row.appendChild(statusCell);
                
                // Actions
                const actionsCell = document.createElement('td');
                actionsCell.innerHTML = `
                    <a href="view-appraisal.html?id=${appraisal.id}" class="btn btn-info btn-sm">
                        <i class="fas fa-eye"></i> View
                    </a>
                    ${!appraisal.manager_signature ? `
                    <a href="create-appraisal.html?id=${appraisal.id}" class="btn btn-primary btn-sm ml-1">
                        <i class="fas fa-edit"></i> Edit
                    </a>
                    ` : ''}
                `;
                row.appendChild(actionsCell);
                
                tableBody.appendChild(row);
            });
        }

        // Navigation button press effects
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.nav-link');

            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    // Remove pressed class from all nav links
                    navLinks.forEach(nav => nav.classList.remove('pressed'));

                    // Add pressed class to clicked link
                    this.classList.add('pressed');
                });
            });
        });

    </script>
</body>
</html>