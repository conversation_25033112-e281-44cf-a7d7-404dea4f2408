<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Team - HR Performance Evaluation System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    
    <!-- SheetJS (XLSX) -->
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/styles.css">

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <style>
        /* Modern color palette for charts */
        :root {
            --chart-primary: #4a90e2;
            --chart-secondary: #7bb3f0;
            --chart-success: #27ae60;
            --chart-info: #3498db;
            --chart-warning: #f39c12;
            --chart-danger: #e74c3c;
            --chart-purple: #9b59b6;
            --chart-orange: #e67e22;
            --chart-teal: #1abc9c;
            --chart-pink: #e91e63;
        }

        /* Interactive filter button styles */
        .filter-btn-active {
            background-color: #e74c3c !important;
            border-color: #e74c3c !important;
            color: white !important;
        }

        /* Enhanced card styling */
        .card.border-danger {
            border-width: 2px;
            box-shadow: 0 4px 8px rgba(220, 53, 69, 0.2);
        }

        .badge-danger {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        /* Card hover effects */
        .card {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
            border-radius: 8px;
            border: none;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            background: linear-gradient(135deg, #4a90e2 0%, #7bb3f0 100%);
            color: white;
            border-radius: 8px 8px 0 0;
            border: none;
            padding: 10px 15px;
        }

        .card-body {
            padding: 10px;
        }

        /* Status badges */
        .badge-danger {
            background: linear-gradient(45deg, #dc3545, #c82333);
        }

        .badge-success {
            background: linear-gradient(45deg, #28a745, #1e7e34);
        }

        /* Button improvements */
        .btn-sm {
            border-radius: 20px;
            font-weight: 500;
        }

        /* Table improvements */
        .table-hover tbody tr:hover {
            background-color: rgba(0, 123, 255, 0.05);
        }

        /* View toggle button */
        #viewToggleBtn {
            transition: all 0.3s ease;
        }

        #viewToggleBtn:hover {
            transform: scale(1.05);
        }

        /* Filter section styling */
        .filter-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        /* Navigation button press effects */
        .nav-link {
            transition: all 0.3s ease;
            transform: scale(1);
        }

        .nav-link.pressed {
            transform: scale(0.9);
            background-color: #0056b3 !important;
            color: white !important;
        }

        .nav-link.active {
            transform: scale(0.9);
            background-color: #0056b3 !important;
            color: white !important;
        }

        /* Dark mode support */
        body.dark-mode .filter-section {
            background: #2d2d2d;
            border: 1px solid #444444;
            color: #e0e0e0;
        }

        body.dark-mode .card {
            background-color: #1e1e1e;
            border-color: #333333;
            color: #e0e0e0;
        }

        body.dark-mode .card-header {
            background-color: rgba(255, 255, 255, 0.05);
            border-color: #333333;
            color: #e0e0e0;
        }

        body.dark-mode .table {
            color: #e0e0e0;
        }

        body.dark-mode .table thead th {
            background-color: rgba(255, 255, 255, 0.05);
            color: #e0e0e0;
        }

        body.dark-mode .form-label {
            color: #e0e0e0;
        }

        body.dark-mode h1, body.dark-mode h2, body.dark-mode h3 {
            color: #e0e0e0;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-top">
                <div class="logo-section">
                    <i class="fas fa-chart-line logo-icon"></i>
                    <span class="logo-text">Performance Evaluation System</span>
                </div>
                <div class="user-info">
                    <span id="currentUserName" class="user-name">Loading...</span>
                    <a href="#" id="logoutBtn" class="logout-btn">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
            </div>
            <nav class="nav-bottom">
                <a href="team.html" class="nav-link active">
                    <i class="fas fa-users"></i> My Team
                </a>
                <a href="appraisals.html" class="nav-link">
                    <i class="fas fa-clipboard-check"></i> Appraisals
                </a>
                <a href="reports.html" class="nav-link">
                    <i class="fas fa-chart-bar"></i> Reports
                </a>
                <a href="my-appraisal.html" class="nav-link">
                    <i class="fas fa-user-check"></i> My Appraisal
                </a>
            </nav>
        </div>
    </header>
    
    <!-- Main content -->
    <main class="main">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h1 class="text-2xl font-bold">My Team</h1>
                <button id="exportTeamBtn" class="btn btn-success">
                    <i class="fas fa-download mr-1"></i> Export to Excel
                </button>
            </div>

            <!-- Filter Section -->
            <div class="filter-section mb-3">
                <div class="row align-items-end">
                    <div class="col-md-4">
                        <label for="directTeamFilter" class="form-label mb-1">
                            <i class="fas fa-users"></i> My Direct Team Filter
                        </label>
                        <select id="directTeamFilter" class="form-select form-select-sm" onchange="applyDirectTeamFilter()">
                            <option value="">All Direct Reports</option>
                            <!-- Direct team members will be loaded dynamically -->
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="extendedTeamManagersFilter" class="form-label mb-1">
                            <i class="fas fa-user-tie"></i> My Extended Team Managers
                        </label>
                        <select id="extendedTeamManagersFilter" class="form-select form-select-sm" onchange="applyExtendedTeamManagersFilter()" disabled>
                            <option value="">Select a parent manager first</option>
                            <!-- Extended team managers will be loaded dynamically -->
                        </select>
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="button" class="btn btn-secondary btn-sm" onclick="resetFilters()">
                            <i class="fas fa-undo"></i> Reset
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Team Members Cards -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center py-2">
                    <h5 class="mb-0">My Direct Reports</h5>
                    <div>
                        <button id="viewToggleBtn" class="btn btn-outline-light btn-sm">
                            <i class="fas fa-table me-1"></i> Table View
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Card View (Default) -->
                    <div id="cardView">
                        <div id="teamCardsContainer" class="row">
                            <!-- Team member cards will be loaded dynamically -->
                        </div>
                    </div>

                    <!-- Table View (Hidden by default) -->
                    <div id="tableView" style="display: none;">
                        <div class="table-responsive">
                            <table id="teamTable" class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Code</th>
                                        <th>Name</th>
                                        <th>Position</th>
                                        <th>Department</th>
                                        <th>Last Appraisal</th>
                                        <th>Score</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="teamTableBody">
                                    <!-- Team members will be loaded dynamically -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Extended Team View (for parent managers) -->
            <div id="extendedTeamSection" class="card mt-4" style="display: none;">
                <div class="card-header py-2">
                    <h5 class="mb-1">Extended Team Overview</h5>
                    <p class="mb-0 small opacity-75">Appraisal status for your managers' teams</p>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="extendedTeamTable" class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Employee</th>
                                    <th>Position</th>
                                    <th>Department</th>
                                    <th>Manager</th>
                                    <th>Last Appraisal</th>
                                    <th>Score</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="extendedTeamTableBody">
                                <!-- Extended team members will be loaded dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p class="footer-text">© Produced by: Dr. Ahmed Atef - All rights reserved.</p>
        </div>
    </footer>
    
    <!-- Dark mode toggle -->
    <div id="darkModeToggle" class="dark-mode-toggle" title="Switch to Dark Mode">
        <i class="fas fa-moon"></i>
    </div>
    
    <!-- Supabase JS -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2/dist/umd/supabase.js"></script>
    
    <!-- Custom JS -->
    <script src="../assets/js/utils.js"></script>
    <script src="../assets/js/supabase.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication and role
            if (!appAuth.checkAuth('manager')) {
                return;
            }

            // User name will be loaded with manager data

            // Initialize enhanced navigation
            appUtils.initializeNavigation();

            // Apply dark mode if previously enabled
            appUtils.applyDarkMode();
            
            // Dark mode toggle
            document.getElementById('darkModeToggle').addEventListener('click', function() {
                appUtils.toggleDarkMode();
            });
            
            // Logout button
            document.getElementById('logoutBtn').addEventListener('click', function(e) {
                e.preventDefault();
                appAuth.logoutUser();
            });
            
            // Load team members
            loadTeamMembers();

            // View toggle button
            document.getElementById('viewToggleBtn').addEventListener('click', toggleView);

            // Export to Excel button
            document.getElementById('exportTeamBtn').addEventListener('click', function() {
                appUtils.exportToExcel('teamTable', 'My_Team');
            });
        });
        
        // Load team members
        async function loadTeamMembers() {
            try {
                console.log('Manager team: Loading team members...');
                // Get current manager
                const currentManager = appAuth.getCurrentEmployee();
                console.log('Manager team: Current manager:', currentManager);
                if (!currentManager) {
                    appUtils.showNotification('Manager information not found', 'error');
                    return;
                }

                // Update user name display
                document.getElementById('currentUserName').textContent = currentManager.name;

                // Get team members
                const { data: teamMembers, error } = await supabaseClient
                    .from('employees')
                    .select('*')
                    .eq('manager_code', currentManager.code_number)
                    .order('name');

                if (error) throw error;

                if (!teamMembers || teamMembers.length === 0) {
                    document.getElementById('teamCardsContainer').innerHTML =
                        '<div class="col-12"><p class="text-center">No team members found</p></div>';
                    allTeamMembers = [];
                    filteredTeamMembers = [];
                    return;
                }

                // Get employee codes
                const employeeCodes = teamMembers.map(emp => emp.code_number);

                // Get manager information for team members
                const managerCodes = [...new Set(teamMembers.map(emp => emp.manager_code).filter(code => code))];
                const { data: managers } = await supabaseClient
                    .from('employees')
                    .select('code_number, name')
                    .in('code_number', managerCodes);

                const managerMap = {};
                (managers || []).forEach(manager => {
                    managerMap[manager.code_number] = manager.name;
                });

                // Get latest appraisals for each team member
                const { data: latestAppraisals } = await supabaseClient
                    .from('appraisals')
                    .select(`
                        id,
                        employee_code,
                        total_score,
                        grade,
                        created_at,
                        period:appraisal_periods(name)
                    `)
                    .in('employee_code', employeeCodes)
                    .order('created_at', { ascending: false });

                // Get pending assignments
                const { data: pendingAssignments } = await supabaseClient
                    .from('appraisal_assignments')
                    .select(`
                        employee_code,
                        status,
                        assigned_at,
                        period_id,
                        period:appraisal_periods(id, name)
                    `)
                    .in('employee_code', employeeCodes)
                    .eq('status', 'pending');

                // Create maps for quick lookup
                const appraisalMap = {};
                const assignmentMap = {};

                (latestAppraisals || []).forEach(appraisal => {
                    if (!appraisalMap[appraisal.employee_code]) {
                        appraisalMap[appraisal.employee_code] = appraisal;
                    }
                });

                (pendingAssignments || []).forEach(assignment => {
                    if (!assignmentMap[assignment.employee_code]) {
                        assignmentMap[assignment.employee_code] = assignment;
                    }
                });

                // Combine data
                const teamMembersWithData = teamMembers.map(employee => ({
                    ...employee,
                    manager_name: managerMap[employee.manager_code] || currentManager.name,
                    latestAppraisal: appraisalMap[employee.code_number] || null,
                    pendingAssignment: assignmentMap[employee.code_number] || null
                }));

                // Store data globally for filtering
                allTeamMembers = teamMembersWithData;
                filteredTeamMembers = teamMembersWithData;

                // Populate direct team filter dropdown
                populateDirectTeamFilter(teamMembersWithData);

                // Populate both views
                populateTeamCards(filteredTeamMembers);
                populateTeamTable(filteredTeamMembers);

                // Check if current manager is a parent manager (has managers as direct reports)
                const hasManagerReports = teamMembers.some(emp => emp.is_manager);
                if (hasManagerReports) {
                    loadExtendedTeam(teamMembers.filter(emp => emp.is_manager));
                }

            } catch (error) {
                console.error('Error loading team members:', error);
                appUtils.showNotification('Error loading team members', 'error');
            }
        }

        // Toggle between card and table view
        function toggleView() {
            const cardView = document.getElementById('cardView');
            const tableView = document.getElementById('tableView');
            const toggleBtn = document.getElementById('viewToggleBtn');

            if (cardView.style.display === 'none') {
                // Switch to card view
                cardView.style.display = 'block';
                tableView.style.display = 'none';
                toggleBtn.innerHTML = '<i class="fas fa-table mr-1"></i> Table View';
            } else {
                // Switch to table view
                cardView.style.display = 'none';
                tableView.style.display = 'block';
                toggleBtn.innerHTML = '<i class="fas fa-th-large mr-1"></i> Card View';
            }
        }

        // Populate team cards
        function populateTeamCards(teamMembers) {
            const container = document.getElementById('teamCardsContainer');
            container.innerHTML = '';

            if (teamMembers.length === 0) {
                container.innerHTML = '<div class="col-12"><p class="text-center">No team members found</p></div>';
                return;
            }

            teamMembers.forEach(employee => {
                const hasPendingAssignment = employee.pendingAssignment;
                const cardClass = hasPendingAssignment ? 'border-danger' : 'border-light';
                const statusBadge = hasPendingAssignment ?
                    `<span class="badge badge-danger mb-2">PENDING APPRAISAL</span>` : '';

                const lastAppraisal = employee.latestAppraisal ?
                    `<p class="card-text"><small class="text-muted">Last: ${employee.latestAppraisal.period ? employee.latestAppraisal.period.name : 'N/A'}</small></p>` :
                    '<p class="card-text"><small class="text-muted">No appraisals yet</small></p>';

                const score = employee.latestAppraisal ?
                    `<p class="card-text"><strong>Score: ${employee.latestAppraisal.total_score}%</strong></p>` :
                    '<p class="card-text"><em>No score available</em></p>';

                // Managers can create appraisals when there are pending assignments
                const appraisalUrl = hasPendingAssignment ?
                    `create-appraisal.html?employee=${employee.code_number}&period=${employee.pendingAssignment.period_id}` :
                    null;

                const card = document.createElement('div');
                card.className = 'col-md-6 col-lg-4 mb-3';
                card.innerHTML = `
                    <div class="card ${cardClass} h-100 shadow-sm" style="border-radius: 12px; overflow: hidden;">
                        <div class="card-header py-2" style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); border-bottom: 1px solid #e0e0e0;">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0 font-weight-bold text-primary">${employee.name}</h6>
                                ${statusBadge}
                            </div>
                        </div>
                        <div class="card-body py-2 px-3" style="background: #fafafa;">
                            <div class="row no-gutters mb-2">
                                <div class="col-6">
                                    <small class="text-muted d-block">Position</small>
                                    <span class="font-weight-medium" style="font-size: 0.85rem;">${employee.position}</span>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted d-block">Department</small>
                                    <span class="font-weight-medium" style="font-size: 0.85rem;">${employee.department}</span>
                                </div>
                            </div>
                            <div class="row no-gutters mb-2">
                                <div class="col-6">
                                    <small class="text-muted d-block">Last Appraisal</small>
                                    <span style="font-size: 0.8rem;">${employee.latestAppraisal ? (employee.latestAppraisal.period ? employee.latestAppraisal.period.name : 'N/A') : 'None'}</span>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted d-block">Performance</small>
                                    ${employee.latestAppraisal ?
                                        `<span class="badge" style="background-color: ${getGradeColor(employee.latestAppraisal.grade)}; color: ${employee.latestAppraisal.grade === 'Excellent' ? '#000' : '#fff'}; font-size: 0.75rem;">
                                            ${employee.latestAppraisal.total_score}%
                                        </span>` :
                                        '<span style="font-size: 0.8rem; color: #6c757d;">No score</span>'
                                    }
                                </div>
                            </div>
                            <div class="d-flex justify-content-end mt-2">
                                <button class="btn btn-outline-primary btn-sm" onclick="showEmployeeKPIs('${employee.code_number}', '${employee.name}')" title="View KPIs" style="font-size: 0.75rem; width: 80px;">
                                    <i class="fas fa-tasks"></i> KPIs
                                </button>
                            </div>
                        </div>
                        <div class="card-footer py-2 px-3" style="background: #f8f9fa; border-top: 1px solid #e0e0e0;">
                            <div class="d-flex justify-content-between">
                                ${appraisalUrl ?
                                    `<a href="${appraisalUrl}" class="btn btn-primary btn-sm flex-fill mr-1" style="font-size: 0.75rem;">
                                        <i class="fas fa-clipboard-check"></i> Appraise
                                    </a>` :
                                    `<button class="btn btn-secondary btn-sm flex-fill mr-1" disabled title="No appraisal assignment" style="font-size: 0.75rem;">
                                        <i class="fas fa-clipboard-check"></i> No Assignment
                                    </button>`
                                }
                                ${employee.latestAppraisal ?
                                    `<a href="view-appraisal.html?id=${employee.latestAppraisal.id}" class="btn btn-info btn-sm flex-fill ml-1" style="font-size: 0.75rem;">
                                        <i class="fas fa-eye"></i> View
                                    </a>` :
                                    `<button class="btn btn-secondary btn-sm flex-fill ml-1" disabled title="No appraisal available" style="font-size: 0.75rem;">
                                        <i class="fas fa-eye"></i> No Appraisal
                                    </button>`
                                }
                            </div>
                        </div>
                    </div>
                `;
                container.appendChild(card);
            });
        }

        // Populate team table
        function populateTeamTable(teamMembers) {
            const tableBody = document.getElementById('teamTableBody');
            tableBody.innerHTML = '';
            
            if (teamMembers.length === 0) {
                const row = document.createElement('tr');
                const cell = document.createElement('td');
                cell.colSpan = 8;
                cell.textContent = 'No team members found';
                cell.className = 'text-center';
                row.appendChild(cell);
                tableBody.appendChild(row);
                return;
            }
            
            teamMembers.forEach(employee => {
                const row = document.createElement('tr');
                
                // Code
                const codeCell = document.createElement('td');
                codeCell.textContent = employee.code_number;
                row.appendChild(codeCell);
                
                // Name
                const nameCell = document.createElement('td');
                nameCell.textContent = employee.name;
                row.appendChild(nameCell);
                
                // Position
                const positionCell = document.createElement('td');
                positionCell.textContent = employee.position;
                row.appendChild(positionCell);
                
                // Department
                const departmentCell = document.createElement('td');
                departmentCell.textContent = employee.department;
                row.appendChild(departmentCell);
                
                // Last Appraisal
                const appraisalCell = document.createElement('td');
                if (employee.latestAppraisal && employee.latestAppraisal.period) {
                    appraisalCell.textContent = employee.latestAppraisal.period.name;
                } else {
                    appraisalCell.textContent = 'None';
                }
                row.appendChild(appraisalCell);
                
                // Score
                const scoreCell = document.createElement('td');
                if (employee.latestAppraisal && employee.latestAppraisal.total_score) {
                    scoreCell.textContent = employee.latestAppraisal.total_score.toFixed(1) + '%';
                    scoreCell.style.color = appUtils.getGradeColor(employee.latestAppraisal.grade);
                } else {
                    scoreCell.textContent = 'N/A';
                }
                row.appendChild(scoreCell);

                // Status
                const statusCell = document.createElement('td');
                if (employee.pendingAssignment) {
                    statusCell.innerHTML = '<span class="badge badge-danger">PENDING APPRAISAL</span>';
                } else {
                    statusCell.innerHTML = '<span class="badge badge-success">UP TO DATE</span>';
                }
                row.appendChild(statusCell);

                // Actions
                const actionsCell = document.createElement('td');
                // Managers can create appraisals when there are pending assignments
                const appraisalUrl = employee.pendingAssignment ?
                    `create-appraisal.html?employee=${employee.code_number}&period=${employee.pendingAssignment.period_id}` :
                    null;

                actionsCell.innerHTML = `
                    <button class="btn btn-outline-primary btn-sm me-1" onclick="showEmployeeKPIs('${employee.code_number}', '${employee.name}')" title="View KPIs">
                        <i class="fas fa-tasks"></i> KPIs
                    </button>
                    ${appraisalUrl ?
                        `<a href="${appraisalUrl}" class="btn btn-primary btn-sm">
                            <i class="fas fa-clipboard-check"></i> Appraise
                        </a>` :
                        `<button class="btn btn-secondary btn-sm" disabled title="No appraisal assignment for this employee">
                            <i class="fas fa-clipboard-check"></i> No Assignment
                        </button>`
                    }
                    ${employee.latestAppraisal ?
                        `<a href="view-appraisal.html?id=${employee.latestAppraisal.id}" class="btn btn-info btn-sm ml-1">
                            <i class="fas fa-eye"></i> View
                        </a>` :
                        `<button class="btn btn-secondary btn-sm ml-1" disabled title="No appraisal available">
                            <i class="fas fa-eye"></i> No Appraisal
                        </button>`
                    }
                `;
                row.appendChild(actionsCell);
                
                tableBody.appendChild(row);
            });
        }

        // Recursive function to get complete team hierarchy
        async function getCompleteTeamHierarchy(managerCode, visited = new Set()) {
            // Prevent infinite loops
            if (visited.has(managerCode)) {
                return [];
            }
            visited.add(managerCode);

            try {
                // Get direct reports
                const { data: directReports, error } = await supabaseClient
                    .from('employees')
                    .select('*')
                    .eq('manager_code', managerCode);

                if (error) throw error;

                let allTeamMembers = directReports || [];

                // Recursively get team members for each direct report
                for (const employee of directReports || []) {
                    const subTeam = await getCompleteTeamHierarchy(employee.code_number, new Set(visited));
                    allTeamMembers = allTeamMembers.concat(subTeam);
                }

                return allTeamMembers;
            } catch (error) {
                console.error('Error getting team hierarchy for manager', managerCode, ':', error);
                return [];
            }
        }

        // Load extended team for parent managers - Enhanced with complete hierarchy
        async function loadExtendedTeam(managerReports) {
            try {
                if (!managerReports || managerReports.length === 0) {
                    return;
                }

                // Get complete hierarchy for all manager reports
                let allExtendedTeamMembers = [];
                for (const manager of managerReports) {
                    const managerTeam = await getCompleteTeamHierarchy(manager.code_number);
                    allExtendedTeamMembers = allExtendedTeamMembers.concat(managerTeam);
                }

                if (allExtendedTeamMembers.length === 0) {
                    return;
                }

                // Remove duplicates (in case of overlapping hierarchies)
                const uniqueMembers = allExtendedTeamMembers.filter((member, index, self) =>
                    index === self.findIndex(m => m.code_number === member.code_number)
                );

                const extendedTeamMembers = uniqueMembers;

                // Get employee codes
                const employeeCodes = extendedTeamMembers.map(emp => emp.code_number);

                // Get latest appraisals
                const { data: latestAppraisals } = await supabaseClient
                    .from('appraisals')
                    .select(`
                        id,
                        employee_code,
                        total_score,
                        grade,
                        created_at,
                        period:appraisal_periods(name)
                    `)
                    .in('employee_code', employeeCodes)
                    .order('created_at', { ascending: false });

                // Get pending assignments
                const { data: pendingAssignments } = await supabaseClient
                    .from('appraisal_assignments')
                    .select(`
                        employee_code,
                        status,
                        assigned_at,
                        period_id,
                        period:appraisal_periods(id, name)
                    `)
                    .in('employee_code', employeeCodes)
                    .eq('status', 'pending');

                // Create maps for quick lookup
                const appraisalMap = {};
                const assignmentMap = {};
                const managerMap = {};

                (latestAppraisals || []).forEach(appraisal => {
                    if (!appraisalMap[appraisal.employee_code]) {
                        appraisalMap[appraisal.employee_code] = appraisal;
                    }
                });

                (pendingAssignments || []).forEach(assignment => {
                    if (!assignmentMap[assignment.employee_code]) {
                        assignmentMap[assignment.employee_code] = assignment;
                    }
                });

                // Get all unique manager codes from extended team members
                const allManagerCodes = [...new Set(extendedTeamMembers.map(emp => emp.manager_code).filter(code => code))];

                // Load all manager information
                const { data: allManagers } = await supabaseClient
                    .from('employees')
                    .select('code_number, name')
                    .in('code_number', allManagerCodes);

                // Build complete manager map
                (allManagers || []).forEach(manager => {
                    managerMap[manager.code_number] = manager.name;
                });

                // Combine data
                const extendedTeamWithData = extendedTeamMembers.map(employee => ({
                    ...employee,
                    managerName: managerMap[employee.manager_code] || 'Unknown',
                    latestAppraisal: appraisalMap[employee.code_number] || null,
                    pendingAssignment: assignmentMap[employee.code_number] || null
                }));

                // Show extended team section and populate table
                document.getElementById('extendedTeamSection').style.display = 'block';
                populateExtendedTeamTable(extendedTeamWithData);

            } catch (error) {
                console.error('Error loading extended team:', error);
                appUtils.showNotification('Error loading extended team', 'error');
            }
        }

        // Populate extended team table
        function populateExtendedTeamTable(extendedTeamMembers) {
            const tableBody = document.getElementById('extendedTeamTableBody');
            tableBody.innerHTML = '';

            if (extendedTeamMembers.length === 0) {
                const row = document.createElement('tr');
                const cell = document.createElement('td');
                cell.colSpan = 8;
                cell.textContent = 'No extended team members found';
                cell.className = 'text-center';
                row.appendChild(cell);
                tableBody.appendChild(row);
                return;
            }

            extendedTeamMembers.forEach(employee => {
                const row = document.createElement('tr');

                // Employee
                const employeeCell = document.createElement('td');
                employeeCell.textContent = employee.name;
                row.appendChild(employeeCell);

                // Position
                const positionCell = document.createElement('td');
                positionCell.textContent = employee.position;
                row.appendChild(positionCell);

                // Department
                const departmentCell = document.createElement('td');
                departmentCell.textContent = employee.department;
                row.appendChild(departmentCell);

                // Manager
                const managerCell = document.createElement('td');
                managerCell.textContent = employee.managerName;
                row.appendChild(managerCell);

                // Last Appraisal
                const appraisalCell = document.createElement('td');
                if (employee.latestAppraisal && employee.latestAppraisal.period) {
                    appraisalCell.textContent = employee.latestAppraisal.period.name;
                } else {
                    appraisalCell.textContent = 'None';
                }
                row.appendChild(appraisalCell);

                // Score
                const scoreCell = document.createElement('td');
                if (employee.latestAppraisal && employee.latestAppraisal.total_score) {
                    scoreCell.textContent = employee.latestAppraisal.total_score.toFixed(1) + '%';
                    scoreCell.style.color = appUtils.getGradeColor(employee.latestAppraisal.grade);
                } else {
                    scoreCell.textContent = 'N/A';
                }
                row.appendChild(scoreCell);

                // Status
                const statusCell = document.createElement('td');
                if (employee.pendingAssignment) {
                    statusCell.innerHTML = '<span class="badge badge-danger">PENDING APPRAISAL</span>';
                } else {
                    statusCell.innerHTML = '<span class="badge badge-success">UP TO DATE</span>';
                }
                row.appendChild(statusCell);

                // Actions
                const actionsCell = document.createElement('td');
                actionsCell.innerHTML = `
                    <button class="btn btn-outline-primary btn-sm me-1" onclick="showEmployeeKPIs('${employee.code_number}', '${employee.name}')" title="View KPIs">
                        <i class="fas fa-tasks"></i> KPIs
                    </button>
                    ${employee.latestAppraisal ?
                        `<a href="view-appraisal.html?id=${employee.latestAppraisal.id}" class="btn btn-info btn-sm">
                            <i class="fas fa-eye"></i> View
                        </a>` :
                        `<button class="btn btn-secondary btn-sm" disabled title="No appraisal available">
                            <i class="fas fa-eye"></i> No Appraisal
                        </button>`
                    }
                `;
                row.appendChild(actionsCell);

                tableBody.appendChild(row);
            });
        }



        // Helper function for unified grade colors
        function getGradeColor(grade) {
            switch (grade) {
                case 'Poor': return '#dc3545'; // Red
                case 'Need Improvement': return '#fd7e14'; // Orange
                case 'Meet Requirements':
                case 'Meets Requirements': return '#198754'; // Green
                case 'Very Good': return '#0d6efd'; // Blue
                case 'Excellent': return '#ffc107'; // Yellow
                default: return '#6c757d'; // Gray
            }
        }

        // Helper function for score-based grade colors
        function getScoreGradeColor(score) {
            if (score >= 95) return '#ffc107'; // Excellent - Yellow
            if (score >= 85) return '#0d6efd'; // Very Good - Blue
            if (score >= 75) return '#198754'; // Meets Requirements - Green
            if (score >= 65) return '#fd7e14'; // Need Improvement - Orange
            return '#dc3545';                  // Poor - Red
        }

        // Filter functionality
        let allTeamMembers = [];
        let filteredTeamMembers = [];

        // Populate direct team filter dropdown
        function populateDirectTeamFilter(teamMembers) {
            const directTeamFilter = document.getElementById('directTeamFilter');
            directTeamFilter.innerHTML = '<option value="">All Direct Reports</option>';

            teamMembers.forEach(employee => {
                const option = document.createElement('option');
                option.value = employee.code_number;
                option.textContent = employee.name;
                directTeamFilter.appendChild(option);
            });
        }

        // Apply direct team filter
        async function applyDirectTeamFilter() {
            const selectedEmployeeCode = document.getElementById('directTeamFilter').value;

            if (!selectedEmployeeCode) {
                // Show all direct reports
                filteredTeamMembers = allTeamMembers;
                populateTeamCards(filteredTeamMembers);
                populateTeamTable(filteredTeamMembers);

                // Disable the second filter and show original extended team if applicable
                disableExtendedTeamManagersFilter();

                const hasManagerReports = allTeamMembers.some(emp => emp.is_manager);
                if (hasManagerReports) {
                    // Show the original extended team (all manager reports)
                    loadExtendedTeam(allTeamMembers.filter(emp => emp.is_manager));
                } else {
                    document.getElementById('extendedTeamSection').style.display = 'none';
                }
                return;
            }

            // Filter direct team to show only selected employee
            filteredTeamMembers = allTeamMembers.filter(emp => emp.code_number === selectedEmployeeCode);
            populateTeamCards(filteredTeamMembers);
            populateTeamTable(filteredTeamMembers);

            // Check if selected employee is a manager and load their extended team
            const selectedEmployee = allTeamMembers.find(emp => emp.code_number === selectedEmployeeCode);
            if (selectedEmployee && selectedEmployee.is_manager) {
                // Load extended team for this manager
                await loadExtendedTeamForManager(selectedEmployeeCode);

                // Populate the extended team managers filter with this manager's direct manager reports
                await populateExtendedTeamManagersFilter(selectedEmployeeCode);
            } else {
                // Hide extended team section and disable second filter if selected employee is not a manager
                document.getElementById('extendedTeamSection').style.display = 'none';
                disableExtendedTeamManagersFilter();
            }
        }

        // Populate extended team managers filter based on selected parent manager
        async function populateExtendedTeamManagersFilter(parentManagerCode) {
            try {
                const extendedTeamManagersFilter = document.getElementById('extendedTeamManagersFilter');
                extendedTeamManagersFilter.innerHTML = '<option value="">All Extended Team</option>';

                // Get direct reports of the selected parent manager who are also managers
                const { data: managerReports } = await supabaseClient
                    .from('employees')
                    .select('code_number, name')
                    .eq('manager_code', parentManagerCode)
                    .eq('is_manager', true)
                    .order('name');

                if (managerReports && managerReports.length > 0) {
                    managerReports.forEach(manager => {
                        const option = document.createElement('option');
                        option.value = manager.code_number;
                        option.textContent = manager.name;
                        extendedTeamManagersFilter.appendChild(option);
                    });

                    // Enable the filter
                    extendedTeamManagersFilter.disabled = false;
                } else {
                    // No manager reports, disable the filter
                    extendedTeamManagersFilter.disabled = true;
                }
            } catch (error) {
                console.error('Error loading extended team managers:', error);
                disableExtendedTeamManagersFilter();
            }
        }

        // Disable extended team managers filter
        function disableExtendedTeamManagersFilter() {
            const extendedTeamManagersFilter = document.getElementById('extendedTeamManagersFilter');
            extendedTeamManagersFilter.innerHTML = '<option value="">Select a parent manager first</option>';
            extendedTeamManagersFilter.disabled = true;
        }

        // Apply extended team managers filter
        async function applyExtendedTeamManagersFilter() {
            const selectedManagerCode = document.getElementById('extendedTeamManagersFilter').value;

            if (!selectedManagerCode) {
                // Show all extended team for the currently selected parent manager
                const selectedEmployeeCode = document.getElementById('directTeamFilter').value;
                if (selectedEmployeeCode) {
                    await loadExtendedTeamForManager(selectedEmployeeCode);
                }
                return;
            }

            // Show the selected manager's extended team (complete hierarchy) in extended team table
            await loadExtendedTeamForManager(selectedManagerCode);
        }

        function resetFilters() {
            document.getElementById('directTeamFilter').value = '';
            disableExtendedTeamManagersFilter();
            filteredTeamMembers = allTeamMembers;
            populateTeamCards(filteredTeamMembers);
            populateTeamTable(filteredTeamMembers);

            // Check if current manager has manager reports and show extended team if so
            const hasManagerReports = allTeamMembers.some(emp => emp.is_manager);
            if (hasManagerReports) {
                // Show the original extended team (all manager reports)
                loadExtendedTeam(allTeamMembers.filter(emp => emp.is_manager));
            } else {
                document.getElementById('extendedTeamSection').style.display = 'none';
            }
        }

        // Load extended team for a specific manager
        async function loadExtendedTeamForManager(managerCode) {
            try {
                // Get the complete team hierarchy for this manager
                const managerTeam = await getCompleteTeamHierarchy(managerCode);

                if (managerTeam.length === 0) {
                    document.getElementById('extendedTeamSection').style.display = 'none';
                    return;
                }

                // Get employee codes
                const employeeCodes = managerTeam.map(emp => emp.code_number);

                // Get latest appraisals
                const { data: latestAppraisals } = await supabaseClient
                    .from('appraisals')
                    .select(`
                        id,
                        employee_code,
                        total_score,
                        grade,
                        manager_signature,
                        employee_signature,
                        employee_signature_requested,
                        created_at,
                        period:appraisal_periods(id, name)
                    `)
                    .in('employee_code', employeeCodes)
                    .order('created_at', { ascending: false });

                // Get pending assignments
                const { data: pendingAssignments } = await supabaseClient
                    .from('appraisal_assignments')
                    .select('*')
                    .in('employee_code', employeeCodes)
                    .eq('status', 'pending');

                // Create maps for quick lookup
                const appraisalMap = {};
                const assignmentMap = {};

                (latestAppraisals || []).forEach(appraisal => {
                    if (!appraisalMap[appraisal.employee_code]) {
                        appraisalMap[appraisal.employee_code] = appraisal;
                    }
                });

                (pendingAssignments || []).forEach(assignment => {
                    if (!assignmentMap[assignment.employee_code]) {
                        assignmentMap[assignment.employee_code] = assignment;
                    }
                });

                // Get all unique manager codes from extended team members
                const allManagerCodes = [...new Set(managerTeam.map(emp => emp.manager_code).filter(code => code))];

                // Load all manager information
                const { data: allManagers } = await supabaseClient
                    .from('employees')
                    .select('code_number, name')
                    .in('code_number', allManagerCodes);

                const managerMap = {};
                (allManagers || []).forEach(manager => {
                    managerMap[manager.code_number] = manager.name;
                });

                // Combine data
                const extendedTeamWithData = managerTeam.map(employee => ({
                    ...employee,
                    managerName: managerMap[employee.manager_code] || 'Unknown',
                    latestAppraisal: appraisalMap[employee.code_number] || null,
                    pendingAssignment: assignmentMap[employee.code_number] || null
                }));

                // Show extended team section and populate table
                document.getElementById('extendedTeamSection').style.display = 'block';
                populateExtendedTeamTable(extendedTeamWithData);

            } catch (error) {
                console.error('Error loading extended team for manager:', error);
                appUtils.showNotification('Error loading extended team', 'error');
            }
        }

        // Show employee KPIs in modal
        async function showEmployeeKPIs(employeeCode, employeeName) {
            console.log('showEmployeeKPIs called with:', employeeCode, employeeName);
            try {
                // Update modal title
                document.getElementById('kpisModalLabel').innerHTML = `
                    <i class="fas fa-tasks"></i> KPIs for ${employeeName}
                `;

                // Show loading state
                document.getElementById('kpisContent').innerHTML = `
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading KPIs...</p>
                    </div>
                `;

                // Show modal
                const modal = new bootstrap.Modal(document.getElementById('kpisModal'));
                modal.show();

                // Get current period KPIs for this employee
                const { data: kpiAssignments, error } = await supabaseClient
                    .from('employee_kpis')
                    .select('*')
                    .eq('employee_code', employeeCode)
                    .order('created_at', { ascending: false });

                if (error) {
                    console.error('Error fetching employee KPIs:', error);
                    throw error;
                }

                // Get KPI details and period details separately
                if (kpiAssignments && kpiAssignments.length > 0) {
                    const kpiIds = [...new Set(kpiAssignments.map(k => k.kpi_id))];
                    const periodIds = [...new Set(kpiAssignments.map(k => k.period_id))];

                    // Fetch KPI details
                    const { data: kpisData, error: kpisError } = await supabaseClient
                        .from('kpis')
                        .select('id, name, description, category')
                        .in('id', kpiIds);

                    // Fetch period details
                    const { data: periodsData, error: periodsError } = await supabaseClient
                        .from('appraisal_periods')
                        .select('id, name')
                        .in('id', periodIds);

                    if (kpisError || periodsError) {
                        console.error('Error fetching KPI or period details:', kpisError, periodsError);
                        throw kpisError || periodsError;
                    }

                    // Create lookup maps
                    const kpisMap = {};
                    const periodsMap = {};

                    kpisData?.forEach(kpi => {
                        kpisMap[kpi.id] = kpi;
                    });

                    periodsData?.forEach(period => {
                        periodsMap[period.id] = period;
                    });

                    // Combine the data
                    kpiAssignments.forEach(assignment => {
                        assignment.kpis = kpisMap[assignment.kpi_id];
                        assignment.appraisal_periods = periodsMap[assignment.period_id];
                    });
                }

                if (!kpiAssignments || kpiAssignments.length === 0) {
                    document.getElementById('kpisContent').innerHTML = `
                        <div class="text-center text-muted">
                            <i class="fas fa-info-circle fa-3x mb-3"></i>
                            <h5>No KPIs Assigned</h5>
                            <p>This employee has no KPIs assigned yet.</p>
                        </div>
                    `;
                    return;
                }

                // Group KPIs by category
                const performanceKPIs = kpiAssignments.filter(kpi => kpi.kpis.category === 'performance');
                const behavioralKPIs = kpiAssignments.filter(kpi => kpi.kpis.category === 'behavioral');

                // Generate KPIs HTML
                let kpisHTML = '';

                if (performanceKPIs.length > 0) {
                    kpisHTML += `
                        <div class="mb-4">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-chart-line"></i> Performance KPIs
                            </h6>
                            <div class="row">
                    `;

                    performanceKPIs.forEach(assignment => {
                        kpisHTML += `
                            <div class="col-md-6 mb-3">
                                <div class="card border-primary">
                                    <div class="card-body p-3">
                                        <h6 class="card-title text-primary">${assignment.kpis.name}</h6>
                                        <p class="card-text small text-muted mb-2">${assignment.kpis.description}</p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="badge bg-primary">Weight: ${assignment.weight}%</span>
                                            <small class="text-muted">${assignment.appraisal_periods.name}</small>
                                        </div>
                                        ${assignment.measurement_description ?
                                            `<div class="mt-2">
                                                <small class="text-info">
                                                    <i class="fas fa-ruler"></i> ${assignment.measurement_description}
                                                </small>
                                            </div>` : ''
                                        }
                                    </div>
                                </div>
                            </div>
                        `;
                    });

                    kpisHTML += `
                            </div>
                        </div>
                    `;
                }

                if (behavioralKPIs.length > 0) {
                    kpisHTML += `
                        <div class="mb-4">
                            <h6 class="text-success mb-3">
                                <i class="fas fa-users"></i> Behavioral KPIs
                            </h6>
                            <div class="row">
                    `;

                    behavioralKPIs.forEach(assignment => {
                        kpisHTML += `
                            <div class="col-md-6 mb-3">
                                <div class="card border-success">
                                    <div class="card-body p-3">
                                        <h6 class="card-title text-success">${assignment.kpis.name}</h6>
                                        <p class="card-text small text-muted mb-2">${assignment.kpis.description}</p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="badge bg-success">Weight: ${assignment.weight}%</span>
                                            <small class="text-muted">${assignment.appraisal_periods.name}</small>
                                        </div>
                                        ${assignment.measurement_description ?
                                            `<div class="mt-2">
                                                <small class="text-info">
                                                    <i class="fas fa-ruler"></i> ${assignment.measurement_description}
                                                </small>
                                            </div>` : ''
                                        }
                                    </div>
                                </div>
                            </div>
                        `;
                    });

                    kpisHTML += `
                            </div>
                        </div>
                    `;
                }

                // Calculate total weights
                const totalPerformanceWeight = performanceKPIs.reduce((sum, kpi) => sum + kpi.weight, 0);
                const totalBehavioralWeight = behavioralKPIs.reduce((sum, kpi) => sum + kpi.weight, 0);

                // Add summary
                kpisHTML += `
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="text-primary">Performance KPIs</h6>
                                    <h4 class="text-primary">${performanceKPIs.length}</h4>
                                    <small>Total Weight: ${totalPerformanceWeight}%</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="text-success">Behavioral KPIs</h6>
                                    <h4 class="text-success">${behavioralKPIs.length}</h4>
                                    <small>Total Weight: ${totalBehavioralWeight}%</small>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                document.getElementById('kpisContent').innerHTML = kpisHTML;

            } catch (error) {
                console.error('Error loading employee KPIs:', error);
                document.getElementById('kpisContent').innerHTML = `
                    <div class="text-center text-danger">
                        <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                        <h5>Error Loading KPIs</h5>
                        <p>There was an error loading the KPIs. Please try again.</p>
                    </div>
                `;
            }
        }

        // Navigation button press effects
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.nav-link');

            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    // Remove pressed class from all nav links
                    navLinks.forEach(nav => nav.classList.remove('pressed'));

                    // Add pressed class to clicked link
                    this.classList.add('pressed');
                });
            });
        });
    </script>

    <!-- KPIs Modal -->
    <div class="modal fade" id="kpisModal" tabindex="-1" aria-labelledby="kpisModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="kpisModalLabel">
                        <i class="fas fa-tasks"></i> Employee KPIs
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="kpisContent">
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Loading KPIs...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>