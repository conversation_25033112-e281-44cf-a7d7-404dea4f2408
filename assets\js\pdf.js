// PDF generation functions for HR Performance Evaluation System

// Generate PDF from appraisal data
async function generateAppraisalPDF(appraisalData) {
    // Create a new jsPDF instance
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();
    
    // Set document properties
    doc.setProperties({
        title: `Performance Appraisal - ${appraisalData.employee.name}`,
        subject: `Performance Appraisal for ${appraisalData.period.name}`,
        author: 'HR Performance Evaluation System',
        creator: 'HR Performance Evaluation System'
    });
    
    // Add company logo/header
    doc.setFontSize(18);
    doc.setTextColor(0, 51, 102); // Dark blue
    doc.text('HR PERFORMANCE EVALUATION', 105, 20, { align: 'center' });
    
    // Add appraisal information
    doc.setFontSize(12);
    doc.setTextColor(0, 0, 0); // Black
    doc.text(`Employee: ${appraisalData.employee.name}`, 20, 40);
    doc.text(`Position: ${appraisalData.employee.position}`, 20, 48);
    doc.text(`Department: ${appraisalData.employee.department}`, 20, 56);
    doc.text(`Manager: ${appraisalData.manager.name}`, 20, 64);
    doc.text(`Appraisal Period: ${appraisalData.period.name}`, 20, 72);
    doc.text(`Date: ${appUtils.formatReadableDate(appraisalData.created_at)}`, 20, 80);
    
    // Add horizontal line
    doc.setDrawColor(0, 51, 102); // Dark blue
    doc.setLineWidth(0.5);
    doc.line(20, 85, 190, 85);
    
    // Add section title for Performance KPIs
    doc.setFontSize(14);
    doc.setTextColor(0, 51, 102); // Dark blue
    doc.text('Performance KPIs', 20, 95);
    
    // Add Performance KPIs table
    const performanceKpis = appraisalData.scores.filter(score => score.category === 'Performance');
    let yPos = 105;
    
    // Table header
    doc.setFontSize(10);
    doc.setTextColor(255, 255, 255); // White
    doc.setFillColor(0, 51, 102); // Dark blue
    doc.rect(20, yPos, 100, 7, 'F');
    doc.rect(120, yPos, 20, 7, 'F');
    doc.rect(140, yPos, 20, 7, 'F');
    doc.rect(160, yPos, 30, 7, 'F');
    doc.text('KPI', 21, yPos + 5);
    doc.text('Weight', 121, yPos + 5);
    doc.text('Score', 141, yPos + 5);
    doc.text('Weighted', 161, yPos + 5);
    
    yPos += 7;
    
    // Table rows for Performance KPIs
    doc.setTextColor(0, 0, 0); // Black
    performanceKpis.forEach(kpi => {
        doc.setFillColor(240, 240, 240); // Light gray
        doc.rect(20, yPos, 100, 7, 'F');
        doc.rect(120, yPos, 20, 7, 'F');
        doc.rect(140, yPos, 20, 7, 'F');
        doc.rect(160, yPos, 30, 7, 'F');
        
        doc.text(kpi.name, 21, yPos + 5);
        doc.text(`${kpi.weight}%`, 121, yPos + 5);
        doc.text(`${kpi.score}/5`, 141, yPos + 5);
        
        const weightedScore = appUtils.calculateWeightedScore(kpi.score, kpi.weight);
        doc.text(`${weightedScore.toFixed(2)}%`, 161, yPos + 5);
        
        yPos += 7;
    });
    
    // Add Performance KPIs summary
    doc.setFillColor(220, 220, 220); // Darker gray
    doc.rect(20, yPos, 100, 7, 'F');
    doc.rect(120, yPos, 20, 7, 'F');
    doc.rect(140, yPos, 20, 7, 'F');
    doc.rect(160, yPos, 30, 7, 'F');
    
    doc.setFontSize(10);
    doc.setFont(undefined, 'bold');
    doc.text('Performance Total', 21, yPos + 5);
    
    const performanceWeight = appraisalData.category_weights.find(cw => cw.category === 'Performance').weight;
    doc.text(`${performanceWeight}%`, 121, yPos + 5);
    doc.text('', 141, yPos + 5);
    doc.text(`${appraisalData.performance_score.toFixed(2)}%`, 161, yPos + 5);
    
    doc.setFont(undefined, 'normal');
    
    yPos += 15;
    
    // Add section title for Behavioral KPIs
    doc.setFontSize(14);
    doc.setTextColor(0, 51, 102); // Dark blue
    doc.text('Behavioral KPIs', 20, yPos);
    
    yPos += 10;
    
    // Table header for Behavioral KPIs
    doc.setFontSize(10);
    doc.setTextColor(255, 255, 255); // White
    doc.setFillColor(0, 51, 102); // Dark blue
    doc.rect(20, yPos, 100, 7, 'F');
    doc.rect(120, yPos, 20, 7, 'F');
    doc.rect(140, yPos, 20, 7, 'F');
    doc.rect(160, yPos, 30, 7, 'F');
    doc.text('KPI', 21, yPos + 5);
    doc.text('Weight', 121, yPos + 5);
    doc.text('Score', 141, yPos + 5);
    doc.text('Weighted', 161, yPos + 5);
    
    yPos += 7;
    
    // Table rows for Behavioral KPIs
    doc.setTextColor(0, 0, 0); // Black
    const behavioralKpis = appraisalData.scores.filter(score => score.category === 'Behavioral');
    behavioralKpis.forEach(kpi => {
        doc.setFillColor(240, 240, 240); // Light gray
        doc.rect(20, yPos, 100, 7, 'F');
        doc.rect(120, yPos, 20, 7, 'F');
        doc.rect(140, yPos, 20, 7, 'F');
        doc.rect(160, yPos, 30, 7, 'F');
        
        doc.text(kpi.name, 21, yPos + 5);
        doc.text(`${kpi.weight}%`, 121, yPos + 5);
        doc.text(`${kpi.score}/5`, 141, yPos + 5);
        
        const weightedScore = appUtils.calculateWeightedScore(kpi.score, kpi.weight);
        doc.text(`${weightedScore.toFixed(2)}%`, 161, yPos + 5);
        
        yPos += 7;
    });
    
    // Add Behavioral KPIs summary
    doc.setFillColor(220, 220, 220); // Darker gray
    doc.rect(20, yPos, 100, 7, 'F');
    doc.rect(120, yPos, 20, 7, 'F');
    doc.rect(140, yPos, 20, 7, 'F');
    doc.rect(160, yPos, 30, 7, 'F');
    
    doc.setFontSize(10);
    doc.setFont(undefined, 'bold');
    doc.text('Behavioral Total', 21, yPos + 5);
    
    const behavioralWeight = appraisalData.category_weights.find(cw => cw.category === 'Behavioral').weight;
    doc.text(`${behavioralWeight}%`, 121, yPos + 5);
    doc.text('', 141, yPos + 5);
    doc.text(`${appraisalData.behavioral_score.toFixed(2)}%`, 161, yPos + 5);
    
    doc.setFont(undefined, 'normal');
    
    yPos += 15;
    
    // Check if we need a new page
    if (yPos > 250) {
        doc.addPage();
        yPos = 20;
    }
    
    // Add overall score section
    doc.setFontSize(14);
    doc.setTextColor(0, 51, 102); // Dark blue
    doc.text('Overall Performance', 20, yPos);
    
    yPos += 10;
    
    // Add overall score table
    doc.setFontSize(10);
    doc.setTextColor(255, 255, 255); // White
    doc.setFillColor(0, 51, 102); // Dark blue
    doc.rect(20, yPos, 100, 7, 'F');
    doc.rect(120, yPos, 70, 7, 'F');
    doc.text('Category', 21, yPos + 5);
    doc.text('Score', 121, yPos + 5);
    
    yPos += 7;
    
    // Table rows for overall score
    doc.setTextColor(0, 0, 0); // Black
    
    // Performance row
    doc.setFillColor(240, 240, 240); // Light gray
    doc.rect(20, yPos, 100, 7, 'F');
    doc.rect(120, yPos, 70, 7, 'F');
    doc.text('Performance', 21, yPos + 5);
    doc.text(`${appraisalData.performance_score.toFixed(2)}%`, 121, yPos + 5);
    
    yPos += 7;
    
    // Behavioral row
    doc.setFillColor(240, 240, 240); // Light gray
    doc.rect(20, yPos, 100, 7, 'F');
    doc.rect(120, yPos, 70, 7, 'F');
    doc.text('Behavioral', 21, yPos + 5);
    doc.text(`${appraisalData.behavioral_score.toFixed(2)}%`, 121, yPos + 5);
    
    yPos += 7;
    
    // Total row
    doc.setFillColor(220, 220, 220); // Darker gray
    doc.rect(20, yPos, 100, 7, 'F');
    doc.rect(120, yPos, 70, 7, 'F');
    
    doc.setFont(undefined, 'bold');
    doc.text('Total Score', 21, yPos + 5);
    doc.text(`${appraisalData.total_score.toFixed(2)}%`, 121, yPos + 5);
    
    yPos += 7;
    
    // Grade row
    doc.setFillColor(200, 200, 200); // Even darker gray
    doc.rect(20, yPos, 100, 7, 'F');
    doc.rect(120, yPos, 70, 7, 'F');
    
    doc.text('Grade', 21, yPos + 5);
    doc.text(appraisalData.grade, 121, yPos + 5);
    
    doc.setFont(undefined, 'normal');
    
    yPos += 15;
    
    // Add comments section
    doc.setFontSize(14);
    doc.setTextColor(0, 51, 102); // Dark blue
    doc.text('Comments', 20, yPos);
    
    yPos += 10;
    
    // Add comments box
    doc.setDrawColor(0, 51, 102); // Dark blue
    doc.setFillColor(240, 240, 240); // Light gray
    doc.roundedRect(20, yPos, 170, 30, 2, 2, 'FD');
    
    doc.setFontSize(10);
    doc.setTextColor(0, 0, 0); // Black
    
    // Split comments into multiple lines if needed
    const comments = appraisalData.comments || 'No comments provided.';
    const splitComments = doc.splitTextToSize(comments, 160);
    doc.text(splitComments, 25, yPos + 5);
    
    yPos += 40;
    
    // Add signature section
    doc.setFontSize(12);
    doc.setTextColor(0, 51, 102); // Dark blue
    doc.text('Signatures', 20, yPos);
    
    yPos += 10;
    
    // Manager signature
    doc.setDrawColor(0, 0, 0); // Black
    doc.line(20, yPos, 90, yPos);
    doc.setFontSize(10);
    doc.setTextColor(0, 0, 0); // Black
    doc.text('Manager Signature', 20, yPos + 5);
    
    // Employee signature
    doc.line(120, yPos, 190, yPos);
    doc.text('Employee Signature', 120, yPos + 5);
    
    yPos += 15;
    
    // Date
    doc.line(20, yPos, 90, yPos);
    doc.text('Date', 20, yPos + 5);
    
    // Date
    doc.line(120, yPos, 190, yPos);
    doc.text('Date', 120, yPos + 5);
    
    // Add footer with timestamp
    doc.setFontSize(8);
    doc.setTextColor(128, 128, 128); // Gray
    const timestamp = new Date().toLocaleString();
    doc.text(`Generated on: ${timestamp}`, 105, 285, { align: 'center' });
    
    // Return the PDF as a blob
    return doc.output('blob');
}

// Generate and download PDF
async function downloadAppraisalPDF(appraisalData, filename = 'appraisal.pdf') {
    try {
        const pdfBlob = await generateAppraisalPDF(appraisalData);
        const pdfUrl = URL.createObjectURL(pdfBlob);
        
        // Create a temporary link and trigger download
        const link = document.createElement('a');
        link.href = pdfUrl;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        
        // Clean up
        document.body.removeChild(link);
        setTimeout(() => URL.revokeObjectURL(pdfUrl), 100);
        
        return true;
    } catch (error) {
        console.error('Error generating PDF:', error);
        return false;
    }
}

// Export PDF functions
window.appPDF = {
    generateAppraisalPDF,
    downloadAppraisalPDF
};