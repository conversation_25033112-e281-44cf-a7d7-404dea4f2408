<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KPI Management - HR Performance Evaluation System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    
    <!-- SheetJS (XLSX) -->
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/styles.css">

    <style>
        /* Modern color palette for charts */
        :root {
            --chart-primary: #4a90e2;
            --chart-secondary: #7bb3f0;
            --chart-success: #27ae60;
            --chart-info: #3498db;
            --chart-warning: #f39c12;
            --chart-danger: #e74c3c;
        }

        /* Modern card styling */
        .card {
            border-radius: 8px;
            border: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 15px;
        }

        .card-header {
            background: linear-gradient(135deg, #4a90e2 0%, #7bb3f0 100%);
            color: white;
            border-radius: 8px 8px 0 0;
            border: none;
            padding: 10px 15px;
        }

        .card-body {
            padding: 10px;
        }

        /* Dark mode support */
        body.dark-mode .card {
            background-color: #1e1e1e;
            border-color: #333333;
            color: #e0e0e0;
        }

        body.dark-mode .card-header {
            background: linear-gradient(135deg, #4a90e2 0%, #7bb3f0 100%);
            border-color: #333333;
            color: white;
        }

        body.dark-mode .table {
            color: #e0e0e0;
        }

        body.dark-mode .table thead th {
            background-color: rgba(255, 255, 255, 0.05);
            color: #e0e0e0;
        }

        body.dark-mode .form-label {
            color: #e0e0e0;
        }

        body.dark-mode h1, body.dark-mode h2, body.dark-mode h3, body.dark-mode h5 {
            color: #e0e0e0;
        }

        /* Navigation button press effects */
        .nav-link {
            transition: all 0.3s ease;
            transform: scale(1);
        }

        .nav-link.pressed {
            transform: scale(0.9);
            background-color: #0056b3 !important;
            color: white !important;
        }

        .nav-link.active {
            transform: scale(0.9);
            background-color: #0056b3 !important;
            color: white !important;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-top">
                <div class="logo-section">
                    <i class="fas fa-chart-line logo-icon"></i>
                    <span class="logo-text">Performance Evaluation System</span>
                </div>
                <div class="user-info">
                    <span id="currentUserName" class="user-name">Loading...</span>
                    <a href="#" id="logoutBtn" class="logout-btn">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
            </div>
            <nav class="nav-bottom">
                <a href="index.html" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
                <a href="employees.html" class="nav-link">
                    <i class="fas fa-users"></i> Employees
                </a>
                <a href="kpis.html" class="nav-link active">
                    <i class="fas fa-bullseye"></i> KPIs
                </a>
                <a href="assign-kpis.html" class="nav-link">
                    <i class="fas fa-user-tag"></i> Assign KPIs
                </a>
                <a href="assign-appraisals.html" class="nav-link">
                    <i class="fas fa-clipboard-list"></i> Assign Appraisals
                </a>
                <a href="reports.html" class="nav-link">
                    <i class="fas fa-chart-bar"></i> Reports
                </a>
            </nav>
        </div>
    </header>
    
    <!-- Main content -->
    <main class="main">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="text-2xl font-bold">KPI Management</h1>
                <div>
                    <button id="addKpiBtn" class="btn btn-primary">
                        <i class="fas fa-plus mr-1"></i> Add KPI
                    </button>
                    <button id="addCategoryBtn" class="btn btn-secondary ml-2">
                        <i class="fas fa-plus mr-1"></i> Add Category
                    </button>
                </div>
            </div>
            
            <!-- KPI Categories -->
            <div class="card mb-4">
                <div class="card-header py-2">
                    <h5 class="mb-0">KPI Categories</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="categoriesTable" class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Description</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="categoriesTableBody">
                                <!-- Categories will be loaded dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- KPIs -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center py-2">
                    <h5 class="mb-0">Key Performance Indicators</h5>
                    <div class="form-group mb-0">
                        <select id="categoryFilter" class="form-select form-select-sm">
                            <option value="">All Categories</option>
                            <!-- Categories will be loaded dynamically -->
                        </select>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="kpisTable" class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Category</th>
                                    <th>Description</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="kpisTableBody">
                                <!-- KPIs will be loaded dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Add/Edit Category Modal -->
    <div id="categoryModal" class="modal" style="display: none;">
        <div class="modal-backdrop"></div>
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="categoryModalTitle">Add Category</h3>
                    <button type="button" class="close-modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="categoryForm">
                        <input type="hidden" id="categoryId">
                        
                        <div class="form-group">
                            <label for="categoryName" class="form-label">Name</label>
                            <input type="text" id="categoryName" class="form-control" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="categoryDescription" class="form-label">Description</label>
                            <textarea id="categoryDescription" class="form-control" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary close-modal">Cancel</button>
                    <button type="button" id="saveCategory" class="btn btn-primary">Save</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Add/Edit KPI Modal -->
    <div id="kpiModal" class="modal" style="display: none;">
        <div class="modal-backdrop"></div>
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="kpiModalTitle">Add KPI</h3>
                    <button type="button" class="close-modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="kpiForm">
                        <input type="hidden" id="kpiId">
                        
                        <div class="form-group">
                            <label for="kpiName" class="form-label">Name</label>
                            <input type="text" id="kpiName" class="form-control" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="kpiCategory" class="form-label">Category</label>
                            <select id="kpiCategory" class="form-select" required>
                                <option value="">Select Category</option>
                                <!-- Categories will be loaded dynamically -->
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="kpiDescription" class="form-label">Description</label>
                            <textarea id="kpiDescription" class="form-control" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary close-modal">Cancel</button>
                    <button type="button" id="saveKpi" class="btn btn-primary">Save</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal" style="display: none;">
        <div class="modal-backdrop"></div>
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Confirm Delete</h3>
                    <button type="button" class="close-modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete this item? This action cannot be undone.</p>
                    <p id="deleteItemName" class="font-bold"></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary close-modal">Cancel</button>
                    <button type="button" id="confirmDelete" class="btn btn-danger">Delete</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p class="footer-text">© Produced by: Dr. Ahmed Atef - All rights reserved.</p>
        </div>
    </footer>
    
    <!-- Dark mode toggle -->
    <div id="darkModeToggle" class="dark-mode-toggle" title="Switch to Dark Mode">
        <i class="fas fa-moon"></i>
    </div>
    
    <!-- Supabase JS -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2/dist/umd/supabase.js"></script>
    
    <!-- Custom JS -->
    <script src="../assets/js/utils.js"></script>
    <script src="../assets/js/supabase.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication and role
            if (!appAuth.checkAuth('admin')) {
                return;
            }

            // Load current user name
            loadCurrentUserName();
            
            // Apply dark mode if previously enabled
            appUtils.applyDarkMode();
            
            // Dark mode toggle
            document.getElementById('darkModeToggle').addEventListener('click', function() {
                appUtils.toggleDarkMode();
            });
            
            // Logout button
            document.getElementById('logoutBtn').addEventListener('click', function(e) {
                e.preventDefault();
                appAuth.logoutUser();
            });
            
            // Load categories and KPIs
            loadCategories();
            loadKpis();
            
            // Category filter change
            document.getElementById('categoryFilter').addEventListener('change', function() {
                loadKpis();
            });
            
            // Add Category button
            document.getElementById('addCategoryBtn').addEventListener('click', function() {
                openCategoryModal();
            });
            
            // Add KPI button
            document.getElementById('addKpiBtn').addEventListener('click', function() {
                openKpiModal();
            });
            
            // Save Category button
            document.getElementById('saveCategory').addEventListener('click', function() {
                saveCategory();
            });
            
            // Save KPI button
            document.getElementById('saveKpi').addEventListener('click', function() {
                saveKpi();
            });
            
            // Confirm Delete button
            document.getElementById('confirmDelete').addEventListener('click', function() {
                const type = this.getAttribute('data-type');
                const id = this.getAttribute('data-id');
                
                if (type === 'category') {
                    deleteCategory(id);
                } else if (type === 'kpi') {
                    deleteKpi(id);
                }
            });
            
            // Close modal buttons
            document.querySelectorAll('.close-modal').forEach(button => {
                button.addEventListener('click', function() {
                    closeModals();
                });
            });
        });
        
        // Load categories
        async function loadCategories() {
            try {
                const { data: categories, error } = await supabaseClient
                    .from('kpi_categories')
                    .select('*')
                    .order('name');
                
                if (error) throw error;
                
                // Populate categories table
                populateCategoriesTable(categories || []);
                
                // Populate category filter dropdown
                populateCategoryDropdowns(categories || []);
                
            } catch (error) {
                console.error('Error loading categories:', error);
                appUtils.showNotification('Error loading categories', 'error');
            }
        }
        
        // Load KPIs
        async function loadKpis() {
            try {
                const categoryFilter = document.getElementById('categoryFilter').value;
                
                // Build query
                let query = supabaseClient
                    .from('kpis')
                    .select(`
                        *,
                        kpi_categories(id, name)
                    `);
                
                if (categoryFilter) {
                    query = query.eq('category_id', categoryFilter);
                }
                
                // Execute query
                const { data: kpis, error } = await query;
                
                if (error) throw error;
                
                // Populate KPIs table
                populateKpisTable(kpis || []);
                
            } catch (error) {
                console.error('Error loading KPIs:', error);
                appUtils.showNotification('Error loading KPIs', 'error');
            }
        }
        
        // Populate categories table
        function populateCategoriesTable(categories) {
            const tableBody = document.getElementById('categoriesTableBody');
            tableBody.innerHTML = '';
            
            if (categories.length === 0) {
                const row = document.createElement('tr');
                const cell = document.createElement('td');
                cell.colSpan = 3;
                cell.textContent = 'No categories found';
                cell.className = 'text-center';
                row.appendChild(cell);
                tableBody.appendChild(row);
                return;
            }
            
            categories.forEach(category => {
                const row = document.createElement('tr');
                
                // Name
                const nameCell = document.createElement('td');
                nameCell.textContent = category.name;
                row.appendChild(nameCell);
                
                // Description
                const descriptionCell = document.createElement('td');
                descriptionCell.textContent = category.description || 'No description';
                row.appendChild(descriptionCell);
                
                // Actions
                const actionsCell = document.createElement('td');
                actionsCell.innerHTML = `
                    <button class="btn btn-info btn-sm edit-category" data-id="${category.id}">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-danger btn-sm delete-category" data-id="${category.id}" data-name="${category.name}">
                        <i class="fas fa-trash"></i>
                    </button>
                `;
                row.appendChild(actionsCell);
                
                tableBody.appendChild(row);
            });
            
            // Add event listeners to edit and delete buttons
            document.querySelectorAll('.edit-category').forEach(button => {
                button.addEventListener('click', function() {
                    const categoryId = this.getAttribute('data-id');
                    openCategoryModal(categoryId);
                });
            });
            
            document.querySelectorAll('.delete-category').forEach(button => {
                button.addEventListener('click', function() {
                    const categoryId = this.getAttribute('data-id');
                    const categoryName = this.getAttribute('data-name');
                    openDeleteModal('category', categoryId, categoryName);
                });
            });
        }
        
        // Populate KPIs table
        function populateKpisTable(kpis) {
            const tableBody = document.getElementById('kpisTableBody');
            tableBody.innerHTML = '';
            
            if (kpis.length === 0) {
                const row = document.createElement('tr');
                const cell = document.createElement('td');
                cell.colSpan = 4;
                cell.textContent = 'No KPIs found';
                cell.className = 'text-center';
                row.appendChild(cell);
                tableBody.appendChild(row);
                return;
            }
            
            kpis.forEach(kpi => {
                const row = document.createElement('tr');
                
                // Name
                const nameCell = document.createElement('td');
                nameCell.textContent = kpi.name;
                row.appendChild(nameCell);
                
                // Category
                const categoryCell = document.createElement('td');
                categoryCell.textContent = kpi.kpi_categories ? kpi.kpi_categories.name : 'Unknown';
                row.appendChild(categoryCell);
                
                // Description
                const descriptionCell = document.createElement('td');
                descriptionCell.textContent = kpi.description || 'No description';
                row.appendChild(descriptionCell);
                
                // Actions
                const actionsCell = document.createElement('td');
                actionsCell.innerHTML = `
                    <button class="btn btn-info btn-sm edit-kpi" data-id="${kpi.id}">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-danger btn-sm delete-kpi" data-id="${kpi.id}" data-name="${kpi.name}">
                        <i class="fas fa-trash"></i>
                    </button>
                `;
                row.appendChild(actionsCell);
                
                tableBody.appendChild(row);
            });
            
            // Add event listeners to edit and delete buttons
            document.querySelectorAll('.edit-kpi').forEach(button => {
                button.addEventListener('click', function() {
                    const kpiId = this.getAttribute('data-id');
                    openKpiModal(kpiId);
                });
            });
            
            document.querySelectorAll('.delete-kpi').forEach(button => {
                button.addEventListener('click', function() {
                    const kpiId = this.getAttribute('data-id');
                    const kpiName = this.getAttribute('data-name');
                    openDeleteModal('kpi', kpiId, kpiName);
                });
            });
        }
        
        // Populate category dropdowns
        function populateCategoryDropdowns(categories) {
            const categoryFilter = document.getElementById('categoryFilter');
            const kpiCategory = document.getElementById('kpiCategory');
            
            // Clear existing options except the first one
            while (categoryFilter.options.length > 1) {
                categoryFilter.remove(1);
            }
            
            while (kpiCategory.options.length > 1) {
                kpiCategory.remove(1);
            }
            
            // Add categories to dropdowns
            categories.forEach(category => {
                // For filter
                const filterOption = document.createElement('option');
                filterOption.value = category.id;
                filterOption.textContent = category.name;
                categoryFilter.appendChild(filterOption);
                
                // For form select
                const formOption = document.createElement('option');
                formOption.value = category.id;
                formOption.textContent = category.name;
                kpiCategory.appendChild(formOption);
            });
        }
        
        // Open category modal
        async function openCategoryModal(categoryId = null) {
            // Reset form
            document.getElementById('categoryForm').reset();
            document.getElementById('categoryId').value = '';
            
            // Set modal title
            const modalTitle = document.getElementById('categoryModalTitle');
            modalTitle.textContent = categoryId ? 'Edit Category' : 'Add Category';
            
            if (categoryId) {
                try {
                    // Fetch category data
                    const { data: category, error } = await supabaseClient
                        .from('kpi_categories')
                        .select('*')
                        .eq('id', categoryId)
                        .single();
                    
                    if (error) throw error;
                    
                    if (category) {
                        // Populate form
                        document.getElementById('categoryId').value = category.id;
                        document.getElementById('categoryName').value = category.name;
                        document.getElementById('categoryDescription').value = category.description || '';
                    }
                } catch (error) {
                    console.error('Error fetching category:', error);
                    appUtils.showNotification('Error fetching category data', 'error');
                }
            }
            
            // Show modal
            document.getElementById('categoryModal').style.display = 'block';
        }
        
        // Open KPI modal
        async function openKpiModal(kpiId = null) {
            // Reset form
            document.getElementById('kpiForm').reset();
            document.getElementById('kpiId').value = '';
            
            // Set modal title
            const modalTitle = document.getElementById('kpiModalTitle');
            modalTitle.textContent = kpiId ? 'Edit KPI' : 'Add KPI';
            
            if (kpiId) {
                try {
                    // Fetch KPI data
                    const { data: kpi, error } = await supabaseClient
                        .from('kpis')
                        .select('*')
                        .eq('id', kpiId)
                        .single();
                    
                    if (error) throw error;
                    
                    if (kpi) {
                        // Populate form
                        document.getElementById('kpiId').value = kpi.id;
                        document.getElementById('kpiName').value = kpi.name;
                        document.getElementById('kpiCategory').value = kpi.category_id;
                        document.getElementById('kpiDescription').value = kpi.description || '';
                    }
                } catch (error) {
                    console.error('Error fetching KPI:', error);
                    appUtils.showNotification('Error fetching KPI data', 'error');
                }
            }
            
            // Show modal
            document.getElementById('kpiModal').style.display = 'block';
        }
        
        // Open delete confirmation modal
        function openDeleteModal(type, id, name) {
            document.getElementById('deleteItemName').textContent = name;
            document.getElementById('confirmDelete').setAttribute('data-type', type);
            document.getElementById('confirmDelete').setAttribute('data-id', id);
            document.getElementById('deleteModal').style.display = 'block';
        }
        
        // Close all modals
        function closeModals() {
            document.getElementById('categoryModal').style.display = 'none';
            document.getElementById('kpiModal').style.display = 'none';
            document.getElementById('deleteModal').style.display = 'none';
        }
        
        // Save category
        async function saveCategory() {
            try {
                // Get form values
                const categoryId = document.getElementById('categoryId').value;
                const name = document.getElementById('categoryName').value;
                const description = document.getElementById('categoryDescription').value;
                
                // Validate required fields
                if (!name) {
                    appUtils.showNotification('Please enter a category name', 'error');
                    return;
                }
                
                // Prepare category data
                const categoryData = {
                    name: name,
                    description: description
                };
                
                let result;
                
                if (categoryId) {
                    // Update existing category
                    result = await supabaseClient
                        .from('kpi_categories')
                        .update(categoryData)
                        .eq('id', categoryId);
                } else {
                    // Create new category
                    result = await supabaseClient
                        .from('kpi_categories')
                        .insert(categoryData);
                }
                
                if (result.error) throw result.error;
                
                // Show success message
                appUtils.showNotification(
                    categoryId ? 'Category updated successfully' : 'Category added successfully',
                    'success'
                );
                
                // Close modal and reload categories
                closeModals();
                loadCategories();
                loadKpis(); // Reload KPIs to update category names
                
            } catch (error) {
                console.error('Error saving category:', error);
                appUtils.showNotification('Error saving category', 'error');
            }
        }
        
        // Save KPI
        async function saveKpi() {
            try {
                // Get form values
                const kpiId = document.getElementById('kpiId').value;
                const name = document.getElementById('kpiName').value;
                const categoryId = document.getElementById('kpiCategory').value;
                const description = document.getElementById('kpiDescription').value;
                
                // Validate required fields
                if (!name || !categoryId) {
                    appUtils.showNotification('Please fill in all required fields', 'error');
                    return;
                }
                
                // Prepare KPI data
                const kpiData = {
                    name: name,
                    category_id: categoryId,
                    description: description
                };
                
                let result;
                
                if (kpiId) {
                    // Update existing KPI
                    result = await supabaseClient
                        .from('kpis')
                        .update(kpiData)
                        .eq('id', kpiId);
                } else {
                    // Create new KPI
                    result = await supabaseClient
                        .from('kpis')
                        .insert(kpiData);
                }
                
                if (result.error) throw result.error;
                
                // Show success message
                appUtils.showNotification(
                    kpiId ? 'KPI updated successfully' : 'KPI added successfully',
                    'success'
                );
                
                // Close modal and reload KPIs
                closeModals();
                loadKpis();
                
            } catch (error) {
                console.error('Error saving KPI:', error);
                appUtils.showNotification('Error saving KPI', 'error');
            }
        }
        
        // Delete category
        async function deleteCategory(categoryId) {
            try {
                // Delete category
                const { error } = await supabaseClient
                    .from('kpi_categories')
                    .delete()
                    .eq('id', categoryId);
                
                if (error) throw error;
                
                // Show success message
                appUtils.showNotification('Category deleted successfully', 'success');
                
                // Close modal and reload categories
                closeModals();
                loadCategories();
                loadKpis(); // Reload KPIs to update category names
                
            } catch (error) {
                console.error('Error deleting category:', error);
                appUtils.showNotification('Error deleting category', 'error');
            }
        }
        
        // Delete KPI
        async function deleteKpi(kpiId) {
            try {
                // Delete KPI
                const { error } = await supabaseClient
                    .from('kpis')
                    .delete()
                    .eq('id', kpiId);
                
                if (error) throw error;
                
                // Show success message
                appUtils.showNotification('KPI deleted successfully', 'success');
                
                // Close modal and reload KPIs
                closeModals();
                loadKpis();
                
            } catch (error) {
                console.error('Error deleting KPI:', error);
                appUtils.showNotification('Error deleting KPI', 'error');
            }
        }
    </script>
    
    <style>
        /* Modal styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
        }
        
        .modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }
        
        .modal-dialog {
            position: relative;
            width: 100%;
            max-width: 500px;
            margin: 30px auto;
            animation: modalFadeIn 0.3s;
        }
        
        .modal-content {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            overflow: hidden;
        }
        
        .modal-header {
            padding: 1rem;
            border-bottom: 1px solid var(--card-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-body {
            padding: 1.5rem;
        }
        
        .modal-footer {
            padding: 1rem;
            border-top: 1px solid var(--card-border);
            display: flex;
            justify-content: flex-end;
            gap: 0.5rem;
        }
        
        .close-modal {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--secondary-color);
        }
        
        @keyframes modalFadeIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* Footer styles */
        .footer {
            background-color: var(--card-bg);
            border-top: 1px solid var(--card-border);
            padding: 1rem 0;
            margin-top: 2rem;
        }
        
        .footer-text {
            text-align: center;
            color: var(--secondary-color);
        }
        
        /* Dark mode toggle */
        .dark-mode-toggle {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            z-index: 100;
            transition: var(--transition);
        }
        
        .dark-mode-toggle:hover {
            transform: scale(1.1);
        }
        
        /* Active nav link */
        .nav-link.active {
            font-weight: var(--font-weight-bold);
            text-decoration: underline;
        }
    </style>

    <script>
        // Load current user name
        async function loadCurrentUserName() {
            try {
                const currentUser = appAuth.getCurrentUser();
                if (currentUser && currentUser.username) {
                    document.getElementById('currentUserName').textContent = currentUser.username;
                } else {
                    document.getElementById('currentUserName').textContent = 'Admin User';
                }
            } catch (error) {
                console.error('Error loading user name:', error);
                document.getElementById('currentUserName').textContent = 'Admin User';
            }
        }

        // Navigation button press effects
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.nav-link');

            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    // Remove pressed class from all nav links
                    navLinks.forEach(nav => nav.classList.remove('pressed'));

                    // Add pressed class to clicked link
                    this.classList.add('pressed');
                });
            });
        });
    </script>
</body>
</html>