# HR Performance Evaluation System - Simplified Setup Guide

This guide will help you set up and run the HR Performance Evaluation System with the simplified schema.

## Prerequisites

- A Supabase account (free tier is sufficient)
- A modern web browser (Chrome, Firefox, Edge, etc.)

## Setup Steps

### 1. Create a Supabase Project

1. Go to [Supabase](https://supabase.com/) and sign up or log in
2. Create a new project
3. Note down your Supabase URL and anon key from the API settings

### 2. Set Up the Database

1. In your Supabase project, go to the SQL Editor
2. Copy the contents of `supabase/simplified_schema.sql` from this repository
3. Paste it into the SQL Editor and run the script
4. This will create all necessary tables and insert test data

### 3. Configure the Application

1. Open `assets/js/supabase.js` in a text editor
2. Replace the placeholder values with your actual Supabase URL and anon key:

```javascript
const SUPABASE_URL = 'YOUR_SUPABASE_URL'; // Replace with your actual Supabase URL
const SUPABASE_ANON_KEY = 'YOUR_SUPABASE_ANON_KEY'; // Replace with your actual Supabase anon key
```

### 4. Run the Application

Simply open the `index.html` file in your web browser. No server is required.

## Test Accounts

The system comes with three test accounts:

1. **Admin**
   - Username: admin
   - Password: admin123

2. **Parent Manager**
   - Username: parent_manager
   - Password: manager123

3. **Child Manager**
   - Username: child_manager
   - Password: manager456

## Understanding the Simplified Schema

The simplified schema uses a more straightforward approach:

1. **Employees Table**: Contains all employee information with a unique `code_number` field
2. **Users Table**: Contains user accounts directly linked to employees via the `employee_code` field
3. **Manager Relationships**: Uses `manager_code` instead of `manager_id` to establish reporting relationships

This approach makes it easier to:
- Create and manage employees
- Set up user accounts for managers
- Establish reporting relationships

## How to Create a New Manager

1. Log in as admin
2. Go to the Employees page
3. Click "Add Employee"
4. Fill in the employee details
5. Check the "Is Manager" checkbox
6. Enter a username and password for the manager account
7. Click Save

## How to Assign Employees to a Manager

1. When creating or editing an employee
2. Select the manager from the dropdown list
3. The manager's code number will be stored in the `manager_code` field

## Troubleshooting

### Common Issues

1. **Login Issues**
   - Check the browser console for errors
   - Make sure your Supabase URL and anon key are correctly set
   - Verify that the SQL script ran successfully and created the users table
   - Ensure the Supabase client is properly initialized with `supabase.createClient()`

2. **404 Errors for Pages**
   - Ensure all HTML files are in the correct directories
   - Check that file paths in links are correct
   - Verify that you're using the correct case in filenames (important on some servers)
   - Make sure you're running a web server that can serve all the files

3. **Database Query Errors (400 Bad Request)**
   - This often indicates an issue with the query structure or foreign key references
   - Check the browser console for detailed error messages
   - Verify that the table relationships in the SQL schema match the queries in the code
   - Make sure you're using the correct Supabase client version (we use v2)

4. **Charts Not Displaying**
   - Ensure Chart.js is properly loaded
   - Check if the data being passed to the charts is in the correct format
   - Look for JavaScript errors in the console
   - Verify that the canvas elements exist in the HTML

5. **Missing Data**
   - Verify that the sample data was inserted correctly
   - Check if the queries are filtering out data unexpectedly
   - Ensure that the foreign key relationships are correctly established

6. **Supabase Client Issues**
   - Make sure you're using the UMD version of the Supabase client:
     ```html
     <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2/dist/umd/supabase.js"></script>
     ```
   - Check that the Supabase client is properly initialized
   - Verify that your Supabase project is active and accessible

7. **Running the Application**
   - Use a local web server to serve the files (e.g., Live Server in VS Code)
   - Make sure all files are accessible via the web server
   - Access the application via http://localhost:port instead of file:// URLs

8. **KPI Assignment Issues**
   - If you can't assign KPIs to employees, check that the assign-kpis.html page exists
   - Verify that the links to assign KPIs are correct in the employees.html page
   - Make sure the category weights are properly set up in the database

## Next Steps

After setting up the application, you can:
1. Log in as admin to manage employees and KPIs
2. Log in as a manager to conduct appraisals
3. Explore the different features of the system