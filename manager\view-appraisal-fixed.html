<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Appraisal - HR Performance Evaluation System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- jsPDF -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/styles.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container header-container">
            <div class="logo">HR Performance Evaluation System</div>
            <nav class="nav">
                <a href="index.html" class="nav-link">Dashboard</a>
                <a href="team.html" class="nav-link">My Team</a>
                <a href="appraisals.html" class="nav-link active">Appraisals</a>
                <a href="reports.html" class="nav-link">Reports</a>
                <a href="#" id="logoutBtn" class="nav-link">Logout</a>
            </nav>
        </div>
    </header>
    
    <!-- Main content -->
    <main class="main">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="text-2xl font-bold">Appraisal Report</h1>
                <div>
                    <a href="appraisals.html" class="btn btn-secondary mr-2">
                        <i class="fas fa-arrow-left mr-1"></i> Back to Appraisals
                    </a>
                    <button id="downloadPdfBtn" class="btn btn-primary">
                        <i class="fas fa-download mr-1"></i> Download PDF
                    </button>
                </div>
            </div>
            
            <div class="appraisal-report">
                <div class="report-header">
                    <h2 class="report-title">Performance Appraisal Report</h2>
                    <p class="report-subtitle" id="reportPeriod"></p>
                    
                    <div class="report-info">
                        <div class="info-item">
                            <div class="info-label">Employee</div>
                            <div id="employeeName"></div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">Position</div>
                            <div id="employeePosition"></div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">Department</div>
                            <div id="employeeDepartment"></div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">Manager</div>
                            <div id="managerName"></div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">Date</div>
                            <div id="appraisalDate"></div>
                        </div>
                    </div>
                </div>
                
                <div class="gauge-container">
                    <canvas id="scoreGauge" width="300" height="200"></canvas>
                </div>
                
                <div class="score-summary">
                    <div>
                        <span class="summary-label">Total Score:</span>
                        <span id="totalScore" class="summary-value"></span>
                    </div>
                    <div>
                        <span class="summary-label">Grade:</span>
                        <span id="gradeValue" class="summary-value"></span>
                    </div>
                </div>
                
                <!-- Performance KPIs Section -->
                <div class="report-section">
                    <h3 class="report-section-title">Performance KPIs</h3>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>KPI</th>
                                    <th>Weight</th>
                                    <th>Score</th>
                                    <th>Weighted Score</th>
                                    <th>Comments</th>
                                </tr>
                            </thead>
                            <tbody id="performanceKpisTable">
                                <!-- Performance KPIs will be loaded dynamically -->
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="3">Performance Total</th>
                                    <th id="performanceTotal"></th>
                                    <th></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                
                <!-- Behavioral KPIs Section -->
                <div class="report-section">
                    <h3 class="report-section-title">Behavioral KPIs</h3>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>KPI</th>
                                    <th>Weight</th>
                                    <th>Score</th>
                                    <th>Weighted Score</th>
                                    <th>Comments</th>
                                </tr>
                            </thead>
                            <tbody id="behavioralKpisTable">
                                <!-- Behavioral KPIs will be loaded dynamically -->
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="3">Behavioral Total</th>
                                    <th id="behavioralTotal"></th>
                                    <th></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                
                <!-- Category Weights Section -->
                <div class="report-section">
                    <h3 class="report-section-title">Category Weights</h3>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Category</th>
                                    <th>Weight</th>
                                    <th>Score</th>
                                    <th>Weighted Score</th>
                                </tr>
                            </thead>
                            <tbody id="categoryWeightsTable">
                                <!-- Category weights will be loaded dynamically -->
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="3">Overall Total</th>
                                    <th id="overallTotal"></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                
                <!-- Comments Section -->
                <div class="report-section">
                    <h3 class="report-section-title">Comments</h3>
                    <div class="card">
                        <div class="card-body" id="commentsSection">
                            <!-- Comments will be loaded dynamically -->
                        </div>
                    </div>
                </div>
                
                <!-- Signatures Section -->
                <div class="report-section">
                    <h3 class="report-section-title">Signatures</h3>
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Manager Signature</label>
                                <div id="managerSignature" class="signature-box">
                                    <button id="signAsManager" class="btn btn-primary btn-sm">
                                        <i class="fas fa-signature mr-1"></i> Sign as Manager
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Employee Signature</label>
                                <div id="employeeSignature" class="signature-box">
                                    <button id="signAsEmployee" class="btn btn-primary btn-sm">
                                        <i class="fas fa-signature mr-1"></i> Sign as Employee
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p class="footer-text">© 2023 HR Performance Evaluation System. All rights reserved.</p>
        </div>
    </footer>
    
    <!-- Dark mode toggle -->
    <div id="darkModeToggle" class="dark-mode-toggle" title="Switch to Dark Mode">
        <i class="fas fa-moon"></i>
    </div>
    
    <!-- Supabase JS -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2/dist/umd/supabase.js"></script>
    
    <!-- Custom JS -->
    <script src="../assets/js/utils.js"></script>
    <script src="../assets/js/supabase.js"></script>
    <script src="../assets/js/charts.js"></script>
    <script src="../assets/js/pdf.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication and role
            if (!appAuth.checkAuth('manager')) {
                return;
            }
            
            // Apply dark mode if previously enabled
            appUtils.applyDarkMode();
            
            // Dark mode toggle
            document.getElementById('darkModeToggle').addEventListener('click', function() {
                appUtils.toggleDarkMode();
            });
            
            // Logout button
            document.getElementById('logoutBtn').addEventListener('click', function(e) {
                e.preventDefault();
                appAuth.logoutUser();
            });
            
            // Get URL parameters
            const params = appUtils.getUrlParams();
            const appraisalId = params.id;
            
            if (!appraisalId) {
                appUtils.showNotification('Missing appraisal ID', 'error');
                setTimeout(() => {
                    window.location.href = 'appraisals.html';
                }, 2000);
                return;
            }
            
            // Load appraisal data
            loadAppraisalData(appraisalId);
            
            // Download PDF button
            document.getElementById('downloadPdfBtn').addEventListener('click', function() {
                downloadAppraisalPdf(appraisalId);
            });
            
            // Sign as Manager button
            document.getElementById('signAsManager').addEventListener('click', function() {
                signAppraisal(appraisalId, 'manager');
            });
            
            // Sign as Employee button
            document.getElementById('signAsEmployee').addEventListener('click', function() {
                signAppraisal(appraisalId, 'employee');
            });
        });
        
        // Load appraisal data
        async function loadAppraisalData(appraisalId) {
            try {
                // Get current manager
                const currentManager = appAuth.getCurrentEmployee();
                if (!currentManager) {
                    appUtils.showNotification('Manager information not found', 'error');
                    return;
                }
                
                // Get appraisal data
                const { data: appraisal, error: appraisalError } = await supabaseClient
                    .from('appraisals')
                    .select(`
                        *,
                        employee:employees!appraisals_employee_code_fkey(*),
                        manager:employees!appraisals_manager_code_fkey(*),
                        period:appraisal_periods(*)
                    `)
                    .eq('id', appraisalId)
                    .single();
                
                if (appraisalError) throw appraisalError;
                
                if (!appraisal) {
                    appUtils.showNotification('Appraisal not found', 'error');
                    return;
                }
                
                // Verify that the manager has access to this appraisal
                if (appraisal.manager_code !== currentManager.code_number) {
                    // Check if the employee reports to a manager who reports to the current manager
                    const { data: subManager } = await supabaseClient
                        .from('employees')
                        .select('*')
                        .eq('code_number', appraisal.manager_code)
                        .eq('manager_code', currentManager.code_number)
                        .single();
                    
                    if (!subManager) {
                        appUtils.showNotification('You are not authorized to view this appraisal', 'error');
                        setTimeout(() => {
                            window.location.href = 'appraisals.html';
                        }, 2000);
                        return;
                    }
                }
                
                // Display appraisal information
                document.getElementById('reportPeriod').textContent = appraisal.period ? appraisal.period.name : 'N/A';
                document.getElementById('employeeName').textContent = appraisal.employee ? appraisal.employee.name : 'N/A';
                document.getElementById('employeePosition').textContent = appraisal.employee ? appraisal.employee.position : 'N/A';
                document.getElementById('employeeDepartment').textContent = appraisal.employee ? appraisal.employee.department : 'N/A';
                document.getElementById('managerName').textContent = appraisal.manager ? appraisal.manager.name : 'N/A';
                document.getElementById('appraisalDate').textContent = appUtils.formatDate(appraisal.created_at);
                
                // Display scores
                document.getElementById('totalScore').textContent = `${appraisal.total_score.toFixed(1)}%`;
                document.getElementById('gradeValue').textContent = appraisal.grade || 'N/A';
                
                // Create gauge chart
                createScoreGauge('scoreGauge', appraisal.total_score);
                
                // Load KPI scores
                loadKpiScores(appraisalId);
                
                // Load category weights
                loadCategoryWeights(appraisalId);
                
                // Display comments
                document.getElementById('commentsSection').textContent = appraisal.comments || 'No comments provided.';
                
                // Check signatures
                if (appraisal.manager_signed) {
                    document.getElementById('managerSignature').innerHTML = `
                        <div class="signature-info">
                            <div class="signature-name">${appraisal.manager.name}</div>
                            <div class="signature-date">${appUtils.formatDate(appraisal.manager_signed_date)}</div>
                        </div>
                    `;
                    document.getElementById('signAsManager').style.display = 'none';
                }
                
                if (appraisal.employee_signed) {
                    document.getElementById('employeeSignature').innerHTML = `
                        <div class="signature-info">
                            <div class="signature-name">${appraisal.employee.name}</div>
                            <div class="signature-date">${appUtils.formatDate(appraisal.employee_signed_date)}</div>
                        </div>
                    `;
                    document.getElementById('signAsEmployee').style.display = 'none';
                }
                
            } catch (error) {
                console.error('Error loading appraisal data:', error);
                appUtils.showNotification('Error loading appraisal data', 'error');
            }
        }
        
        // Load KPI scores
        async function loadKpiScores(appraisalId) {
            try {
                // Get KPI scores
                const { data: kpiScores, error } = await supabaseClient
                    .from('appraisal_kpi_scores')
                    .select(`
                        *,
                        kpi:kpis(*)
                    `)
                    .eq('appraisal_id', appraisalId);
                
                if (error) throw error;
                
                if (!kpiScores || kpiScores.length === 0) {
                    return;
                }
                
                // Separate performance and behavioral KPIs
                const performanceKpis = kpiScores.filter(score => score.kpi.category_id === 1);
                const behavioralKpis = kpiScores.filter(score => score.kpi.category_id === 2);
                
                // Populate performance KPIs table
                const performanceTable = document.getElementById('performanceKpisTable');
                let performanceTotal = 0;
                
                performanceKpis.forEach(score => {
                    const row = document.createElement('tr');
                    
                    // KPI name
                    const nameCell = document.createElement('td');
                    nameCell.textContent = score.kpi.name;
                    row.appendChild(nameCell);
                    
                    // Weight
                    const weightCell = document.createElement('td');
                    weightCell.textContent = `${score.weight}%`;
                    row.appendChild(weightCell);
                    
                    // Score
                    const scoreCell = document.createElement('td');
                    scoreCell.textContent = score.score;
                    row.appendChild(scoreCell);
                    
                    // Weighted score
                    const weightedScore = (score.score * score.weight) / 100;
                    performanceTotal += weightedScore;
                    
                    const weightedScoreCell = document.createElement('td');
                    weightedScoreCell.textContent = weightedScore.toFixed(2);
                    row.appendChild(weightedScoreCell);
                    
                    // Comments
                    const commentsCell = document.createElement('td');
                    commentsCell.textContent = score.comments || '';
                    row.appendChild(commentsCell);
                    
                    performanceTable.appendChild(row);
                });
                
                document.getElementById('performanceTotal').textContent = performanceTotal.toFixed(2);
                
                // Populate behavioral KPIs table
                const behavioralTable = document.getElementById('behavioralKpisTable');
                let behavioralTotal = 0;
                
                behavioralKpis.forEach(score => {
                    const row = document.createElement('tr');
                    
                    // KPI name
                    const nameCell = document.createElement('td');
                    nameCell.textContent = score.kpi.name;
                    row.appendChild(nameCell);
                    
                    // Weight
                    const weightCell = document.createElement('td');
                    weightCell.textContent = `${score.weight}%`;
                    row.appendChild(weightCell);
                    
                    // Score
                    const scoreCell = document.createElement('td');
                    scoreCell.textContent = score.score;
                    row.appendChild(scoreCell);
                    
                    // Weighted score
                    const weightedScore = (score.score * score.weight) / 100;
                    behavioralTotal += weightedScore;
                    
                    const weightedScoreCell = document.createElement('td');
                    weightedScoreCell.textContent = weightedScore.toFixed(2);
                    row.appendChild(weightedScoreCell);
                    
                    // Comments
                    const commentsCell = document.createElement('td');
                    commentsCell.textContent = score.comments || '';
                    row.appendChild(commentsCell);
                    
                    behavioralTable.appendChild(row);
                });
                
                document.getElementById('behavioralTotal').textContent = behavioralTotal.toFixed(2);
                
            } catch (error) {
                console.error('Error loading KPI scores:', error);
                appUtils.showNotification('Error loading KPI scores', 'error');
            }
        }
        
        // Load category weights
        async function loadCategoryWeights(appraisalId) {
            try {
                // Get appraisal data
                const { data: appraisal, error } = await supabaseClient
                    .from('appraisals')
                    .select(`
                        *,
                        employee:employees!appraisals_employee_code_fkey(*),
                        manager:employees!appraisals_manager_code_fkey(*)
                    `)
                    .eq('id', appraisalId)
                    .single();
                
                if (error) throw error;
                
                if (!appraisal) {
                    return;
                }
                
                // Populate category weights table
                const categoryTable = document.getElementById('categoryWeightsTable');
                
                // Performance row
                const performanceRow = document.createElement('tr');
                
                const performanceNameCell = document.createElement('td');
                performanceNameCell.textContent = 'Performance';
                performanceRow.appendChild(performanceNameCell);
                
                const performanceWeightCell = document.createElement('td');
                performanceWeightCell.textContent = `${appraisal.performance_weight}%`;
                performanceRow.appendChild(performanceWeightCell);
                
                const performanceScoreCell = document.createElement('td');
                performanceScoreCell.textContent = appraisal.performance_score.toFixed(2);
                performanceRow.appendChild(performanceScoreCell);
                
                const performanceWeightedCell = document.createElement('td');
                const performanceWeighted = (appraisal.performance_score * appraisal.performance_weight) / 100;
                performanceWeightedCell.textContent = performanceWeighted.toFixed(2);
                performanceRow.appendChild(performanceWeightedCell);
                
                categoryTable.appendChild(performanceRow);
                
                // Behavioral row
                const behavioralRow = document.createElement('tr');
                
                const behavioralNameCell = document.createElement('td');
                behavioralNameCell.textContent = 'Behavioral';
                behavioralRow.appendChild(behavioralNameCell);
                
                const behavioralWeightCell = document.createElement('td');
                behavioralWeightCell.textContent = `${appraisal.behavioral_weight}%`;
                behavioralRow.appendChild(behavioralWeightCell);
                
                const behavioralScoreCell = document.createElement('td');
                behavioralScoreCell.textContent = appraisal.behavioral_score.toFixed(2);
                behavioralRow.appendChild(behavioralScoreCell);
                
                const behavioralWeightedCell = document.createElement('td');
                const behavioralWeighted = (appraisal.behavioral_score * appraisal.behavioral_weight) / 100;
                behavioralWeightedCell.textContent = behavioralWeighted.toFixed(2);
                behavioralRow.appendChild(behavioralWeightedCell);
                
                categoryTable.appendChild(behavioralRow);
                
                // Update overall total
                document.getElementById('overallTotal').textContent = appraisal.total_score.toFixed(2);
                
            } catch (error) {
                console.error('Error loading category weights:', error);
                appUtils.showNotification('Error loading category weights', 'error');
            }
        }
        
        // Sign appraisal
        async function signAppraisal(appraisalId, signatureType) {
            try {
                const currentUser = appAuth.getCurrentUser();
                const currentEmployee = appAuth.getCurrentEmployee();
                
                if (!currentUser || !currentEmployee) {
                    appUtils.showNotification('User information not found', 'error');
                    return;
                }
                
                // Get appraisal data
                const { data: appraisal, error: appraisalError } = await supabaseClient
                    .from('appraisals')
                    .select('*')
                    .eq('id', appraisalId)
                    .single();
                
                if (appraisalError) throw appraisalError;
                
                if (!appraisal) {
                    appUtils.showNotification('Appraisal not found', 'error');
                    return;
                }
                
                // Update signature fields
                const updateData = {};
                
                if (signatureType === 'manager') {
                    // Verify that the current user is the manager
                    if (appraisal.manager_code !== currentEmployee.code_number) {
                        appUtils.showNotification('You are not authorized to sign as the manager', 'error');
                        return;
                    }
                    
                    updateData.manager_signed = true;
                    updateData.manager_signed_date = new Date().toISOString();
                } else if (signatureType === 'employee') {
                    // For demo purposes, allow managers to sign as employees
                    updateData.employee_signed = true;
                    updateData.employee_signed_date = new Date().toISOString();
                }
                
                // Update appraisal
                const { error: updateError } = await supabaseClient
                    .from('appraisals')
                    .update(updateData)
                    .eq('id', appraisalId);
                
                if (updateError) throw updateError;
                
                appUtils.showNotification('Signature added successfully', 'success');
                
                // Reload the page to show the signature
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
                
            } catch (error) {
                console.error('Error signing appraisal:', error);
                appUtils.showNotification('Error signing appraisal', 'error');
            }
        }
        
        // Download appraisal as PDF
        async function downloadAppraisalPdf(appraisalId) {
            try {
                // Get appraisal data
                const { data: appraisal, error } = await supabaseClient
                    .from('appraisals')
                    .select(`
                        *,
                        employee:employees!appraisals_employee_code_fkey(*),
                        manager:employees!appraisals_manager_code_fkey(*),
                        period:appraisal_periods(*)
                    `)
                    .eq('id', appraisalId)
                    .single();
                
                if (error) throw error;
                
                if (!appraisal) {
                    appUtils.showNotification('Appraisal not found', 'error');
                    return;
                }
                
                // Generate PDF
                const pdfTitle = `Appraisal_${appraisal.employee.name}_${appraisal.period.name}`.replace(/\s+/g, '_');
                appPdf.generateAppraisalPdf(appraisal, pdfTitle);
                
            } catch (error) {
                console.error('Error downloading PDF:', error);
                appUtils.showNotification('Error downloading PDF', 'error');
            }
        }
    </script>
</body>
</html>