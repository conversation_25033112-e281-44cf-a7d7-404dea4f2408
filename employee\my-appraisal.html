<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Appraisal - HR Performance Evaluation System</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/styles.css">
    <style>
        /* My Appraisal specific styling */
        .chart-container {
            position: relative;
            height: 250px;
            margin: 10px 0;
        }

        .chart-container-compact {
            position: relative;
            height: 200px;
            margin: 5px 0;
        }

        /* Navigation button press effects */
        .nav-link {
            transition: all 0.3s ease;
            transform: scale(1);
        }

        .nav-link.pressed {
            transform: scale(0.9);
            background-color: #0056b3 !important;
            color: white !important;
        }

        .nav-link.active {
            transform: scale(0.9);
            background-color: #0056b3 !important;
            color: white !important;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-top">
                <div class="logo-section">
                    <i class="fas fa-chart-line logo-icon"></i>
                    <span class="logo-text">Performance Evaluation System</span>
                </div>
                <div class="user-info">
                    <span id="currentUserName" class="user-name">Loading...</span>
                    <a href="#" id="logoutBtn" class="logout-btn">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
            </div>
        <nav class="nav-bottom">
            <a href="my-profile.html" class="nav-link">
                <i class="fas fa-user"></i> My Profile
            </a>
            <a href="my-appraisal.html" class="nav-link active">
                <i class="fas fa-chart-bar"></i> My Appraisal
            </a>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <h1 class="page-title">My Latest Performance Appraisal</h1>

            <!-- Loading State -->
            <div id="loadingState" class="loading">
                <i class="fas fa-spinner fa-spin fa-2x"></i>
                <p>Loading your appraisal data...</p>
            </div>

            <!-- Latest Appraisal Summary -->
            <div id="latestAppraisalSection" style="display: none;">
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-value" id="totalScore">-</div>
                            <div class="stat-label">Total Score</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-value" id="performanceScore">-</div>
                            <div class="stat-label">Performance Score</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-value" id="behavioralScore">-</div>
                            <div class="stat-label">Behavioral Score</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-value" id="performanceGrade">-</div>
                            <div class="stat-label">Performance Grade</div>
                        </div>
                    </div>
                </div>

                <!-- Charts Row -->
                <div class="row mb-4">
                    <!-- Total Score Chart -->
                    <div class="col-md-12 mb-4">
                        <div class="chart-card">
                            <div class="chart-header">
                                <h5><i class="fas fa-chart-pie"></i> Total Score</h5>
                            </div>
                            <div class="chart-container">
                                <canvas id="totalScoreChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Performance and Behavioral Charts Row -->
                <div class="row mb-4">
                    <!-- Performance Chart -->
                    <div class="col-md-6">
                        <div class="chart-card">
                            <div class="chart-header">
                                <h5><i class="fas fa-chart-pie"></i> Performance %</h5>
                            </div>
                            <div class="chart-container-compact">
                                <canvas id="performanceChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- Behavioral Chart -->
                    <div class="col-md-6">
                        <div class="chart-card">
                            <div class="chart-header">
                                <h5><i class="fas fa-chart-pie"></i> Behavioral %</h5>
                            </div>
                            <div class="chart-container-compact">
                                <canvas id="behavioralChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Appraisal History -->
                <div class="row">
                    <div class="col-12">
                        <div class="chart-card">
                            <div class="chart-header">
                                <h5><i class="fas fa-history"></i> Appraisal History</h5>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Period</th>
                                            <th>Total Score</th>
                                            <th>Performance</th>
                                            <th>Behavioral</th>
                                            <th>Grade</th>
                                            <th>Date</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody id="appraisalHistoryTable">
                                        <!-- History will be loaded here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- No Appraisal State -->
            <div id="noAppraisalState" style="display: none;">
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle fa-2x mb-3"></i>
                    <h4>No Appraisal Data Available</h4>
                    <p>You haven't been evaluated yet. Please contact your manager for more information.</p>
                </div>
            </div>
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2/dist/umd/supabase.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/supabase.js"></script>
    <script src="../assets/js/utils.js"></script>

    <script>
        let currentEmployee = null;
        let totalScoreChart = null;
        let performanceChart = null;
        let behavioralChart = null;

        document.addEventListener('DOMContentLoaded', async function() {
            // Check authentication
            if (!appAuth.checkAuth('employee')) {
                return;
            }

            // Initialize enhanced navigation
            appUtils.initializeNavigation();

            // Apply dark mode if previously enabled
            appUtils.applyDarkMode();

            // Logout functionality
            document.getElementById('logoutBtn').addEventListener('click', function(e) {
                e.preventDefault();
                appAuth.logoutUser();
            });

            // Load appraisal data
            await loadAppraisalData();
        });

        // Load appraisal data
        async function loadAppraisalData() {
            try {
                console.log('Employee Appraisal: Loading appraisal data...');
                
                // Get current employee
                currentEmployee = appAuth.getCurrentEmployee();
                console.log('Employee Appraisal: Current employee:', currentEmployee);
                if (!currentEmployee) {
                    appUtils.showNotification('Employee information not found', 'error');
                    return;
                }

                // Update user name display
                document.getElementById('currentUserName').textContent = currentEmployee.name;

                // Load latest appraisal
                await loadLatestAppraisal();
                
                // Load appraisal history
                await loadAppraisalHistory();

            } catch (error) {
                console.error('Error loading appraisal data:', error);
                appUtils.showNotification('Error loading appraisal data', 'error');
                showNoAppraisalState();
            }
        }

        // Load latest appraisal
        async function loadLatestAppraisal() {
            try {
                const { data: latestAppraisal, error } = await supabaseClient
                    .from('appraisals')
                    .select('*')
                    .eq('employee_code', currentEmployee.code_number)
                    .order('created_at', { ascending: false })
                    .limit(1);

                if (error && error.code !== 'PGRST116') {
                    throw error;
                }

                if (latestAppraisal && latestAppraisal.length > 0) {
                    displayLatestAppraisal(latestAppraisal[0]);
                    createCharts(latestAppraisal[0]);
                    document.getElementById('latestAppraisalSection').style.display = 'block';
                } else {
                    showNoAppraisalState();
                }

                document.getElementById('loadingState').style.display = 'none';

            } catch (error) {
                console.error('Error loading latest appraisal:', error);
                showNoAppraisalState();
            }
        }

        // Display latest appraisal
        function displayLatestAppraisal(appraisal) {
            const totalScore = parseFloat(appraisal.total_score) || 0;
            const performanceScore = parseFloat(appraisal.performance_score) || 0;
            const behavioralScore = parseFloat(appraisal.behavioral_score) || 0;
            const grade = appraisal.grade || 'N/A';

            document.getElementById('totalScore').textContent = totalScore.toFixed(1) + '%';
            document.getElementById('performanceScore').textContent = performanceScore.toFixed(1) + '%';
            document.getElementById('behavioralScore').textContent = behavioralScore.toFixed(1) + '%';
            document.getElementById('performanceGrade').textContent = grade;
        }

        // Create charts
        function createCharts(appraisal) {
            const totalScore = parseFloat(appraisal.total_score) || 0;
            const performanceScore = parseFloat(appraisal.performance_score) || 0;
            const behavioralScore = parseFloat(appraisal.behavioral_score) || 0;

            // Destroy existing charts
            if (totalScoreChart) totalScoreChart.destroy();
            if (performanceChart) performanceChart.destroy();
            if (behavioralChart) behavioralChart.destroy();

            // Create Total Score Chart
            const totalCtx = document.getElementById('totalScoreChart').getContext('2d');
            totalScoreChart = new Chart(totalCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Score', 'Remaining'],
                    datasets: [{
                        data: [totalScore, 100 - totalScore],
                        backgroundColor: ['#3498db', '#ecf0f1'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.label + ': ' + context.parsed + '%';
                                }
                            }
                        }
                    },
                    cutout: '70%'
                },
                plugins: [{
                    beforeDraw: function(chart) {
                        const width = chart.width;
                        const height = chart.height;
                        const ctx = chart.ctx;

                        ctx.restore();
                        const fontSize = (height / 114).toFixed(2);
                        ctx.font = fontSize + "em sans-serif";
                        ctx.textBaseline = "middle";
                        ctx.fillStyle = "#333";

                        const text = totalScore.toFixed(1) + "%";
                        const textX = Math.round((width - ctx.measureText(text).width) / 2);
                        const textY = height / 2;

                        ctx.fillText(text, textX, textY);
                        ctx.save();
                    }
                }]
            });

            // Create Performance Chart
            const perfCtx = document.getElementById('performanceChart').getContext('2d');
            performanceChart = new Chart(perfCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Performance', 'Remaining'],
                    datasets: [{
                        data: [performanceScore, 100 - performanceScore],
                        backgroundColor: ['#27ae60', '#ecf0f1'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.label + ': ' + context.parsed + '%';
                                }
                            }
                        }
                    },
                    cutout: '70%'
                },
                plugins: [{
                    beforeDraw: function(chart) {
                        const width = chart.width;
                        const height = chart.height;
                        const ctx = chart.ctx;

                        ctx.restore();
                        const fontSize = (height / 114).toFixed(2);
                        ctx.font = fontSize + "em sans-serif";
                        ctx.textBaseline = "middle";
                        ctx.fillStyle = "#333";

                        const text = performanceScore.toFixed(1) + "%";
                        const textX = Math.round((width - ctx.measureText(text).width) / 2);
                        const textY = height / 2;

                        ctx.fillText(text, textX, textY);
                        ctx.save();
                    }
                }]
            });

            // Create Behavioral Chart
            const behCtx = document.getElementById('behavioralChart').getContext('2d');
            behavioralChart = new Chart(behCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Behavioral', 'Remaining'],
                    datasets: [{
                        data: [behavioralScore, 100 - behavioralScore],
                        backgroundColor: ['#e74c3c', '#ecf0f1'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.label + ': ' + context.parsed + '%';
                                }
                            }
                        }
                    },
                    cutout: '70%'
                },
                plugins: [{
                    beforeDraw: function(chart) {
                        const width = chart.width;
                        const height = chart.height;
                        const ctx = chart.ctx;

                        ctx.restore();
                        const fontSize = (height / 114).toFixed(2);
                        ctx.font = fontSize + "em sans-serif";
                        ctx.textBaseline = "middle";
                        ctx.fillStyle = "#333";

                        const text = behavioralScore.toFixed(1) + "%";
                        const textX = Math.round((width - ctx.measureText(text).width) / 2);
                        const textY = height / 2;

                        ctx.fillText(text, textX, textY);
                        ctx.save();
                    }
                }]
            });
        }

        // Load appraisal history
        async function loadAppraisalHistory() {
            try {
                const { data: appraisals, error } = await supabaseClient
                    .from('appraisals')
                    .select(`
                        *,
                        period:appraisal_periods(name)
                    `)
                    .eq('employee_code', currentEmployee.code_number)
                    .order('created_at', { ascending: false });

                if (error) {
                    throw error;
                }

                displayAppraisalHistory(appraisals || []);

            } catch (error) {
                console.error('Error loading appraisal history:', error);
                document.getElementById('appraisalHistoryTable').innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center text-muted">Error loading appraisal history</td>
                    </tr>
                `;
            }
        }

        // Display appraisal history
        function displayAppraisalHistory(appraisals) {
            const tableBody = document.getElementById('appraisalHistoryTable');

            if (appraisals.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center text-muted">No appraisal history available</td>
                    </tr>
                `;
                return;
            }

            tableBody.innerHTML = appraisals.map(appraisal => {
                const totalScore = parseFloat(appraisal.total_score) || 0;
                const performanceScore = parseFloat(appraisal.performance_score) || 0;
                const behavioralScore = parseFloat(appraisal.behavioral_score) || 0;
                const grade = appraisal.grade || 'N/A';
                const period = appraisal.period?.name || 'N/A';
                const date = appUtils.formatReadableDate(appraisal.created_at);

                return `
                    <tr>
                        <td>${period}</td>
                        <td>${totalScore.toFixed(1)}%</td>
                        <td>${performanceScore.toFixed(1)}%</td>
                        <td>${behavioralScore.toFixed(1)}%</td>
                        <td><span class="badge ${getGradeBadgeClass(grade)}">${grade}</span></td>
                        <td>${date}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="viewAppraisal('${appraisal.id}')">
                                <i class="fas fa-eye"></i> View
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // Get badge class for grade - Using unified color system
        function getGradeBadgeClass(grade) {
            switch (grade) {
                case 'Poor': return 'badge text-white' + '" style="background-color: #dc3545'; // Red
                case 'Need Improvement': return 'badge text-white' + '" style="background-color: #fd7e14'; // Orange
                case 'Meet Requirements':
                case 'Meets Requirements': return 'badge text-white' + '" style="background-color: #198754'; // Green
                case 'Very Good': return 'badge text-white' + '" style="background-color: #0d6efd'; // Blue
                case 'Excellent': return 'badge text-dark' + '" style="background-color: #ffc107'; // Yellow
                default: return 'badge-secondary';
            }
        }

        // View appraisal function
        function viewAppraisal(appraisalId) {
            window.open(`view-appraisal.html?id=${appraisalId}`, '_blank');
        }

        // Show no appraisal state
        function showNoAppraisalState() {
            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('latestAppraisalSection').style.display = 'none';
            document.getElementById('noAppraisalState').style.display = 'block';
        }

        // Navigation button press effects
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.nav-link');

            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    // Remove pressed class from all nav links
                    navLinks.forEach(nav => nav.classList.remove('pressed'));

                    // Add pressed class to clicked link
                    this.classList.add('pressed');
                });
            });
        });
    </script>
</body>
</html>
