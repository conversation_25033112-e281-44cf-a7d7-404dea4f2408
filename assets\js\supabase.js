// Supabase client initialization
const SUPABASE_URL = 'https://xsdgviorafkfvftabiqk.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhzZGd2aW9yYWZrZnZmdGFiaXFrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE2NzYwNjUsImV4cCI6MjA2NzI1MjA2NX0.nA6-TnCGDBUd--s_vq2T4_Jd7WZxz1mTKFSiMgH24l8';

// Initialize the Supabase client
const supabaseClient = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Authentication functions
async function loginUser(username, password) {
    try {
        console.log('Attempting login for username:', username);
        // Query the users table directly with the simplified schema
        const { data, error } = await supabaseClient
            .from('users')
            .select('id, username, role, employee_code')
            .eq('username', username)
            .eq('password', password)
            .single();

        if (error) throw error;

        console.log('Login query result:', data);

        if (data) {
            // Store user data in localStorage
            localStorage.setItem('user', JSON.stringify(data));
            
            // If user is found, also get their employee data
            if (data.employee_code) {
                const { data: employeeData, error: employeeError } = await supabaseClient
                    .from('employees')
                    .select('*')
                    .eq('code_number', data.employee_code)
                    .single();
                
                if (employeeError) throw employeeError;
                
                if (employeeData) {
                    localStorage.setItem('employee', JSON.stringify(employeeData));
                }
            }
            
            return { success: true, user: data };
        } else {
            return { success: false, message: 'Invalid username or password' };
        }
    } catch (error) {
        console.error('Login error:', error);
        return { success: false, message: 'An error occurred during login' };
    }
}

function logoutUser() {
    localStorage.removeItem('user');
    localStorage.removeItem('employee');
    window.location.href = '../index.html';
}

function getCurrentUser() {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
}

function getCurrentEmployee() {
    const employeeStr = localStorage.getItem('employee');
    return employeeStr ? JSON.parse(employeeStr) : null;
}

function isAuthenticated() {
    return getCurrentUser() !== null;
}

function isAdmin() {
    const user = getCurrentUser();
    return user && user.role === 'admin';
}

function isManager() {
    const user = getCurrentUser();
    return user && user.role === 'manager';
}

function isEmployee() {
    const user = getCurrentUser();
    return user && user.role === 'employee';
}

// Redirect if not authenticated or wrong role
function checkAuth(requiredRole) {
    if (!isAuthenticated()) {
        window.location.href = '../index.html';
        return false;
    }
    
    const user = getCurrentUser();
    if (requiredRole && user.role !== requiredRole) {
        if (user.role === 'admin') {
            window.location.href = '../admin/index.html';
        } else if (user.role === 'manager') {
            window.location.href = '../manager/team.html';
        } else if (user.role === 'employee') {
            window.location.href = '../employee/my-profile.html';
        } else {
            window.location.href = '../index.html';
        }
        return false;
    }
    
    return true;
}

// Export the functions
window.appAuth = {
    loginUser,
    logoutUser,
    getCurrentUser,
    getCurrentEmployee,
    isAuthenticated,
    isAdmin,
    isManager,
    isEmployee,
    checkAuth
};

// Export Supabase client
window.supabaseClient = supabaseClient;