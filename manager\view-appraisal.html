<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Appraisal - Performance Evaluation System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- jsPDF -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/styles.css">

    <style>
        /* Modern card styling */
        .card-header {
            background: linear-gradient(135deg, #4a90e2 0%, #7bb3f0 100%);
            color: white;
            border-radius: 8px 8px 0 0;
            border: none;
            padding: 10px 15px;
        }

        .card-body {
            padding: 10px;
        }

        /* Compact chart containers */
        .chart-container-compact {
            position: relative;
            height: 280px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-top">
                <div class="logo-section">
                    <i class="fas fa-chart-line logo-icon"></i>
                    <span class="logo-text">Performance Evaluation System</span>
                </div>
                <div class="user-info">
                    <span id="currentUserName" class="user-name">Loading...</span>
                    <a href="#" id="logoutBtn" class="logout-btn">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
            </div>
            <nav class="nav-bottom">
                <a href="team.html" class="nav-link">
                    <i class="fas fa-users"></i> My Team
                </a>
                <a href="appraisals.html" class="nav-link active">
                    <i class="fas fa-clipboard-check"></i> Appraisals
                </a>
                <a href="reports.html" class="nav-link">
                    <i class="fas fa-chart-bar"></i> Reports
                </a>
                <a href="my-appraisal.html" class="nav-link">
                    <i class="fas fa-user-check"></i> My Appraisal
                </a>
            </nav>
        </div>
    </header>
    
    <!-- Main content -->
    <main class="main">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="text-2xl font-bold">Appraisal Report</h1>
                <div>
                    <a href="appraisals.html" class="btn btn-secondary mr-2">
                        <i class="fas fa-arrow-left mr-1"></i> Back to Appraisals
                    </a>
                    <button id="downloadPdfBtn" class="btn btn-primary">
                        <i class="fas fa-download mr-1"></i> Download PDF
                    </button>
                </div>
            </div>
            
            <div class="appraisal-report">
                <div class="report-header">
                    <h2 class="report-title">Performance Appraisal Report</h2>
                    <p class="report-subtitle" id="reportPeriod"></p>
                    
                    <div class="report-info">
                        <div class="info-item">
                            <div class="info-label">Employee</div>
                            <div id="employeeName"></div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">Position</div>
                            <div id="employeePosition"></div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">Department</div>
                            <div id="employeeDepartment"></div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">Manager</div>
                            <div id="managerName"></div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">Date</div>
                            <div id="appraisalDate"></div>
                        </div>
                    </div>
                </div>
                
                <!-- Charts Section -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header py-2">
                                <h5 class="mb-0">Total Score</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container-compact">
                                    <canvas id="totalScoreGauge"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header py-2">
                                <h5 class="mb-0">Performance</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container-compact">
                                    <canvas id="performanceGauge"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header py-2">
                                <h5 class="mb-0">Behavioral</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container-compact">
                                    <canvas id="behavioralGauge"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="score-summary">
                    <div>
                        <span class="summary-label">Total Score:</span>
                        <span id="totalScore" class="summary-value"></span>
                    </div>
                    <div>
                        <span class="summary-label">Grade:</span>
                        <span id="gradeValue" class="summary-value"></span>
                    </div>
                </div>
                
                <!-- Performance KPIs Section -->
                <div class="report-section">
                    <h3 class="report-section-title">Performance KPIs</h3>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>KPI</th>
                                    <th>Weight</th>
                                    <th>Score</th>
                                    <th>Weighted Score</th>
                                    <th>Comments</th>
                                </tr>
                            </thead>
                            <tbody id="performanceKpisTable">
                                <!-- Performance KPIs will be loaded dynamically -->
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="3">Performance Total</th>
                                    <th id="performanceTotal"></th>
                                    <th></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                
                <!-- Behavioral KPIs Section -->
                <div class="report-section">
                    <h3 class="report-section-title">Behavioral KPIs</h3>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>KPI</th>
                                    <th>Weight</th>
                                    <th>Score</th>
                                    <th>Weighted Score</th>
                                    <th>Comments</th>
                                </tr>
                            </thead>
                            <tbody id="behavioralKpisTable">
                                <!-- Behavioral KPIs will be loaded dynamically -->
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="3">Behavioral Total</th>
                                    <th id="behavioralTotal"></th>
                                    <th></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                
                <!-- Category Weights Section -->
                <div class="report-section">
                    <h3 class="report-section-title">Category Weights</h3>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Category</th>
                                    <th>Weight</th>
                                    <th>Score</th>
                                    <th>Weighted Score</th>
                                </tr>
                            </thead>
                            <tbody id="categoryWeightsTable">
                                <!-- Category weights will be loaded dynamically -->
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="3">Overall Total</th>
                                    <th id="overallTotal"></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                
                <!-- Comments Section -->
                <div class="report-section">
                    <h3 class="report-section-title">Comments</h3>
                    <div class="card">
                        <div class="card-body" id="commentsSection">
                            <!-- Comments will be loaded dynamically -->
                        </div>
                    </div>
                </div>
                
                <!-- Signatures Section -->
                <div class="report-section">
                    <h3 class="report-section-title">Signatures</h3>
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Manager Signature</label>
                                <div id="managerSignature" class="signature-box">
                                    <button id="signAsManager" class="btn btn-primary btn-sm">
                                        <i class="fas fa-signature mr-1"></i> Sign as Manager
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Employee Signature</label>
                                <div id="employeeSignature" class="signature-box">
                                    <button id="signAsEmployee" class="btn btn-primary btn-sm">
                                        <i class="fas fa-signature mr-1"></i> Sign as Employee
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p class="footer-text">© Produced by: Dr. Ahmed Atef - All rights reserved.</p>
        </div>
    </footer>
    
    <!-- Dark mode toggle -->
    <div id="darkModeToggle" class="dark-mode-toggle" title="Switch to Dark Mode">
        <i class="fas fa-moon"></i>
    </div>
    
    <!-- Supabase JS -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2/dist/umd/supabase.js"></script>
    
    <!-- Custom JS -->
    <script src="../assets/js/utils.js"></script>
    <script src="../assets/js/supabase.js"></script>
    <script src="../assets/js/charts.js"></script>
    <script src="../assets/js/pdf.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication and role
            if (!appAuth.checkAuth('manager')) {
                return;
            }

            // Initialize enhanced navigation
            appUtils.initializeNavigation();

            // Apply dark mode if previously enabled
            appUtils.applyDarkMode();
            
            // Dark mode toggle
            document.getElementById('darkModeToggle').addEventListener('click', function() {
                appUtils.toggleDarkMode();
            });
            
            // Logout button
            document.getElementById('logoutBtn').addEventListener('click', function(e) {
                e.preventDefault();
                appAuth.logoutUser();
            });
            
            // Get URL parameters
            const params = appUtils.getUrlParams();
            const appraisalId = params.id;
            
            if (!appraisalId) {
                appUtils.showNotification('Missing appraisal ID', 'error');
                setTimeout(() => {
                    window.location.href = 'appraisals.html';
                }, 2000);
                return;
            }
            
            // Load appraisal data
            loadAppraisalData(appraisalId);
            
            // Download PDF button
            document.getElementById('downloadPdfBtn').addEventListener('click', function() {
                downloadAppraisalPdf(appraisalId);
            });
            
            // Sign as Manager button
            document.getElementById('signAsManager').addEventListener('click', function() {
                signAppraisal(appraisalId, 'manager');
            });
            
            // Sign as Employee button
            document.getElementById('signAsEmployee').addEventListener('click', function() {
                signAppraisal(appraisalId, 'employee');
            });
        });
        
        // Global chart instances for cleanup
        let totalScoreChart = null;
        let performanceChart = null;
        let behavioralChart = null;

        // Check if a manager has hierarchy access to view an employee's appraisal
        // This function recursively checks up the organizational hierarchy
        async function checkHierarchyAccess(managerCode, employeeCode) {
            try {
                // Get the employee whose appraisal we want to view
                const { data: employee, error: empError } = await supabaseClient
                    .from('employees')
                    .select('code_number, manager_code')
                    .eq('code_number', employeeCode)
                    .single();

                if (empError || !employee) return false;

                // If employee has no manager, no one can view (shouldn't happen)
                if (!employee.manager_code) return false;

                // If the manager is the direct manager of this employee, allow access
                if (employee.manager_code === managerCode) return true;

                // Recursively check if the manager is higher up in the hierarchy
                return await checkHierarchyAccess(managerCode, employee.manager_code);

            } catch (error) {
                console.error('Error checking hierarchy access:', error);
                return false;
            }
        }

        // Load appraisal data
        async function loadAppraisalData(appraisalId) {
            try {
                console.log('View appraisal: Loading appraisal data for ID:', appraisalId);

                // Destroy existing charts before creating new ones
                if (totalScoreChart) {
                    totalScoreChart.destroy();
                    totalScoreChart = null;
                }
                if (performanceChart) {
                    performanceChart.destroy();
                    performanceChart = null;
                }
                if (behavioralChart) {
                    behavioralChart.destroy();
                    behavioralChart = null;
                }
                // Get current manager
                const currentManager = appAuth.getCurrentEmployee();
                console.log('View appraisal: Current manager:', currentManager);
                if (!currentManager) {
                    appUtils.showNotification('Manager information not found', 'error');
                    return;
                }

                // Update user name display
                document.getElementById('currentUserName').textContent = currentManager.name;
                
                // Get appraisal data
                const { data: appraisal, error: appraisalError } = await supabaseClient
                    .from('appraisals')
                    .select(`
                        *,
                        employee:employees!appraisals_employee_code_fkey(*),
                        manager:employees!appraisals_manager_code_fkey(*),
                        period:appraisal_periods(*)
                    `)
                    .eq('id', appraisalId)
                    .single();
                
                if (appraisalError) throw appraisalError;
                
                if (!appraisal) {
                    appUtils.showNotification('Appraisal not found', 'error');
                    return;
                }
                
                // Verify that the manager has access to this appraisal
                // Allow access if: 1) Current user is the direct manager, 2) Current user is anywhere in the hierarchy above, 3) Current user is the employee being appraised
                const isDirectManager = appraisal.manager_code === currentManager.code_number;
                const isEmployeeThemselves = appraisal.employee_code === currentManager.code_number;

                if (!isDirectManager && !isEmployeeThemselves) {
                    // Check if current manager is anywhere in the hierarchy above this appraisal
                    const hasHierarchyAccess = await checkHierarchyAccess(currentManager.code_number, appraisal.employee_code);

                    if (!hasHierarchyAccess) {
                        appUtils.showNotification('You are not authorized to view this appraisal', 'error');
                        setTimeout(() => {
                            window.location.href = 'appraisals.html';
                        }, 2000);
                        return;
                    }
                }
                
                // Display appraisal information
                document.getElementById('reportPeriod').textContent = appraisal.period ? appraisal.period.name : 'N/A';
                document.getElementById('employeeName').textContent = appraisal.employee ? appraisal.employee.name : 'N/A';
                document.getElementById('employeePosition').textContent = appraisal.employee ? appraisal.employee.position : 'N/A';
                document.getElementById('employeeDepartment').textContent = appraisal.employee ? appraisal.employee.department : 'N/A';
                document.getElementById('managerName').textContent = appraisal.manager ? appraisal.manager.name : 'N/A';
                document.getElementById('appraisalDate').textContent = appUtils.formatReadableDate(appraisal.created_at);
                
                // Display scores and grade
                document.getElementById('totalScore').textContent = `${appraisal.total_score.toFixed(1)}%`;
                document.getElementById('gradeValue').textContent = appraisal.grade;
                document.getElementById('gradeValue').className = `summary-value grade-${appraisal.grade.toLowerCase().replace(' ', '-')}`;
                
                // Create gauge charts and store instances for cleanup
                totalScoreChart = appCharts.createGaugeChart('totalScoreGauge', appraisal.total_score, 'Overall Performance');
                performanceChart = appCharts.createGaugeChart('performanceGauge', appraisal.performance_score, 'Performance');
                behavioralChart = appCharts.createGaugeChart('behavioralGauge', appraisal.behavioral_score, 'Behavioral');
                
                // Get appraisal scores
                const { data: appraisalScores, error: scoresError } = await supabaseClient
                    .from('appraisal_scores')
                    .select(`
                        *,
                        employee_kpi:employee_kpis(
                            *,
                            kpi:kpis(
                                *,
                                category:kpi_categories(*)
                            )
                        )
                    `)
                    .eq('appraisal_id', appraisalId)
                    .order('id');
                
                if (scoresError) throw scoresError;

                // Sort scores by employee_kpi created_at to match create-appraisal order
                appraisalScores.sort((a, b) => {
                    const dateA = new Date(a.employee_kpi?.created_at || 0);
                    const dateB = new Date(b.employee_kpi?.created_at || 0);
                    return dateA - dateB;
                });

                // Separate scores by category
                const performanceScores = [];
                const behavioralScores = [];
                
                appraisalScores.forEach(score => {
                    if (score.employee_kpi && score.employee_kpi.kpi && score.employee_kpi.kpi.category) {
                        const category = score.employee_kpi.kpi.category.name;
                        
                        if (category === 'Performance') {
                            performanceScores.push({
                                id: score.id,
                                name: score.employee_kpi.kpi.name,
                                weight: score.employee_kpi.weight,
                                score: score.score,
                                comments: score.comments
                            });
                        } else if (category === 'Behavioral') {
                            behavioralScores.push({
                                id: score.id,
                                name: score.employee_kpi.kpi.name,
                                weight: score.employee_kpi.weight,
                                score: score.score,
                                comments: score.comments
                            });
                        }
                    }
                });
                
                // Display KPI scores
                displayKpiScores('performanceKpisTable', performanceScores);
                displayKpiScores('behavioralKpisTable', behavioralScores);
                
                // Display category weights and scores
                document.getElementById('performanceTotal').textContent = `${appraisal.performance_score.toFixed(1)}%`;
                document.getElementById('behavioralTotal').textContent = `${appraisal.behavioral_score.toFixed(1)}%`;
                
                // Get category weights
                const { data: categoryWeights } = await supabaseClient
                    .from('category_weights')
                    .select(`
                        *,
                        category:kpi_categories(*)
                    `)
                    .eq('employee_code', appraisal.employee_code);
                
                // Display category weights
                displayCategoryWeights('categoryWeightsTable', categoryWeights, appraisal);
                
                document.getElementById('overallTotal').textContent = `${appraisal.total_score.toFixed(1)}%`;
                
                // Display comments
                document.getElementById('commentsSection').textContent = appraisal.comments || 'No comments provided.';
                
                // Update signature buttons
                updateSignatureButtons(appraisal);
                
            } catch (error) {
                console.error('Error loading appraisal data:', error);
                appUtils.showNotification('Error loading appraisal data', 'error');
            }
        }
        
        // Display KPI scores
        function displayKpiScores(tableId, scores) {
            const tableBody = document.getElementById(tableId);
            tableBody.innerHTML = '';
            
            if (scores.length === 0) {
                const row = document.createElement('tr');
                const cell = document.createElement('td');
                cell.colSpan = 5;
                cell.textContent = 'No KPIs found for this category';
                cell.className = 'text-center';
                row.appendChild(cell);
                tableBody.appendChild(row);
                return;
            }
            
            scores.forEach(score => {
                const row = document.createElement('tr');
                
                // KPI Name
                const nameCell = document.createElement('td');
                nameCell.textContent = score.name;
                row.appendChild(nameCell);
                
                // Weight
                const weightCell = document.createElement('td');
                weightCell.textContent = `${score.weight}%`;
                row.appendChild(weightCell);
                
                // Score
                const scoreCell = document.createElement('td');
                scoreCell.textContent = `${score.score}/5`;
                row.appendChild(scoreCell);
                
                // Weighted Score
                const weightedScore = appUtils.calculateWeightedScore(score.score, score.weight);
                const weightedScoreCell = document.createElement('td');
                weightedScoreCell.textContent = `${weightedScore.toFixed(1)}`;
                row.appendChild(weightedScoreCell);
                
                // Comments
                const commentsCell = document.createElement('td');
                commentsCell.textContent = score.comments || 'No comments';
                row.appendChild(commentsCell);
                
                tableBody.appendChild(row);
            });
        }
        
        // Display category weights
        function displayCategoryWeights(tableId, categoryWeights, appraisal) {
            const tableBody = document.getElementById(tableId);
            tableBody.innerHTML = '';
            
            if (!categoryWeights || categoryWeights.length === 0) {
                const row = document.createElement('tr');
                const cell = document.createElement('td');
                cell.colSpan = 4;
                cell.textContent = 'No category weights found';
                cell.className = 'text-center';
                row.appendChild(cell);
                tableBody.appendChild(row);
                return;
            }
            
            categoryWeights.forEach(categoryWeight => {
                if (categoryWeight.category) {
                    const row = document.createElement('tr');
                    
                    // Category Name
                    const nameCell = document.createElement('td');
                    nameCell.textContent = categoryWeight.category.name;
                    row.appendChild(nameCell);
                    
                    // Weight
                    const weightCell = document.createElement('td');
                    weightCell.textContent = `${categoryWeight.weight}%`;
                    row.appendChild(weightCell);
                    
                    // Score
                    const scoreCell = document.createElement('td');
                    const score = categoryWeight.category.name === 'Performance' ? 
                        appraisal.performance_score : 
                        appraisal.behavioral_score;
                    
                    scoreCell.textContent = `${score.toFixed(1)}%`;
                    row.appendChild(scoreCell);
                    
                    // Weighted Score
                    const weightedScore = (score * (categoryWeight.weight / 100));
                    const weightedScoreCell = document.createElement('td');
                    weightedScoreCell.textContent = `${weightedScore.toFixed(1)}%`;
                    row.appendChild(weightedScoreCell);
                    
                    tableBody.appendChild(row);
                }
            });
        }
        
        // Update signature buttons
        function updateSignatureButtons(appraisal) {
            const managerSignatureBox = document.getElementById('managerSignature');
            const employeeSignatureBox = document.getElementById('employeeSignature');
            
            // Manager signature
            if (appraisal.manager_signature) {
                managerSignatureBox.innerHTML = `
                    <div class="signature-signed">
                        <i class="fas fa-check-circle text-success"></i>
                        <span>Signed by ${appraisal.manager.name}</span>
                    </div>
                `;
            } else {
                // Only show sign button if current user is the manager
                const currentManager = appAuth.getCurrentEmployee();
                if (currentManager && currentManager.code_number === appraisal.manager_code) {
                    managerSignatureBox.innerHTML = `
                        <button id="signAsManager" class="btn btn-primary btn-sm">
                            <i class="fas fa-signature mr-1"></i> Sign as Manager
                        </button>
                    `;
                    
                    // Add event listener
                    document.getElementById('signAsManager').addEventListener('click', function() {
                        signAppraisal(appraisal.id, 'manager');
                    });
                } else {
                    managerSignatureBox.innerHTML = `
                        <div class="signature-pending">
                            <i class="fas fa-clock text-warning"></i>
                            <span>Pending signature</span>
                        </div>
                    `;
                }
            }
            
            // Employee signature - NEW WORKFLOW WITH SELF-APPRAISAL DETECTION
            const currentManager = appAuth.getCurrentEmployee();
            const isViewingOwnAppraisal = currentManager && currentManager.code_number === appraisal.employee_code;

            if (appraisal.employee_signature) {
                employeeSignatureBox.innerHTML = `
                    <div class="signature-signed">
                        <i class="fas fa-check-circle text-success"></i>
                        <span>Signed by ${appraisal.employee.name}</span>
                    </div>
                `;
            } else if (isViewingOwnAppraisal) {
                // Manager viewing their own appraisal - show EMPLOYEE interface
                if (appraisal.employee_signature_requested) {
                    // Show sign button when signature is requested
                    employeeSignatureBox.innerHTML = `
                        <button id="signAsEmployee" class="btn btn-success btn-sm">
                            <i class="fas fa-signature mr-1"></i> Sign as Employee
                        </button>
                    `;

                    // Add event listener for employee signing
                    document.getElementById('signAsEmployee').addEventListener('click', function() {
                        signAsEmployeeSelf(appraisal.id);
                    });
                } else {
                    // Waiting for manager to request signature
                    employeeSignatureBox.innerHTML = `
                        <div class="signature-pending">
                            <i class="fas fa-clock text-info"></i>
                            <span>Awaiting signature request from manager</span>
                        </div>
                    `;
                }
            } else if (appraisal.employee_signature_requested) {
                // Manager viewing team member's appraisal - show pending status
                employeeSignatureBox.innerHTML = `
                    <div class="signature-pending">
                        <i class="fas fa-clock text-warning"></i>
                        <span>Pending Employee Signature</span>
                    </div>
                `;
            } else {
                // Manager viewing team member's appraisal - show request signature button
                if (appraisal.manager_signature) {
                    employeeSignatureBox.innerHTML = `
                        <button id="requestEmployeeSignature" class="btn btn-warning btn-sm">
                            <i class="fas fa-paper-plane mr-1"></i> Request Employee Signature
                        </button>
                    `;

                    // Add event listener
                    document.getElementById('requestEmployeeSignature').addEventListener('click', function() {
                        requestEmployeeSignature(appraisal.id);
                    });
                } else {
                    employeeSignatureBox.innerHTML = `
                        <div class="signature-pending">
                            <i class="fas fa-info-circle text-info"></i>
                            <span>Complete manager signature first</span>
                        </div>
                    `;
                }
            }
        }
        
        // Sign appraisal
        async function signAppraisal(appraisalId, signatureType) {
            try {
                // Get current manager
                const currentManager = appAuth.getCurrentEmployee();
                if (!currentManager) {
                    appUtils.showNotification('Manager information not found', 'error');
                    return;
                }
                
                // Get appraisal data
                console.log('Signing appraisal: Loading appraisal data for ID:', appraisalId);
                const { data: appraisal, error: appraisalError } = await supabaseClient
                    .from('appraisals')
                    .select(`
                        *,
                        employee:employees!appraisals_employee_code_fkey(*),
                        manager:employees!appraisals_manager_code_fkey(*)
                    `)
                    .eq('id', appraisalId)
                    .single();

                console.log('Signing appraisal: Appraisal data loaded:', appraisal);
                if (appraisalError) {
                    console.error('Signing appraisal: Error loading appraisal:', appraisalError);
                    throw appraisalError;
                }
                
                if (!appraisal) {
                    appUtils.showNotification('Appraisal not found', 'error');
                    return;
                }
                
                // Verify permissions
                if (signatureType === 'manager' && appraisal.manager_code !== currentManager.code_number) {
                    appUtils.showNotification('You are not authorized to sign as manager', 'error');
                    return;
                }
                
                // Update signature
                const updateData = {};

                if (signatureType === 'manager') {
                    updateData.manager_signature = currentManager.name;
                    updateData.manager_signature_date = new Date().toISOString();
                } else if (signatureType === 'employee') {
                    // This should not be called in the new workflow from manager interface
                    appUtils.showNotification('Employee must sign their own appraisal', 'error');
                    return;
                }
                
                const { error: updateError } = await supabaseClient
                    .from('appraisals')
                    .update(updateData)
                    .eq('id', appraisalId);
                
                if (updateError) throw updateError;
                
                // Show success message
                appUtils.showNotification(`Signed successfully as ${signatureType}`, 'success');
                
                // Reload appraisal data
                loadAppraisalData(appraisalId);
                
            } catch (error) {
                console.error('Error signing appraisal:', error);
                appUtils.showNotification('Error signing appraisal', 'error');
            }
        }

        // Request employee signature - NEW FUNCTION
        async function requestEmployeeSignature(appraisalId) {
            try {
                // Get current manager
                const currentManager = appAuth.getCurrentEmployee();
                if (!currentManager) {
                    appUtils.showNotification('Manager information not found', 'error');
                    return;
                }

                // Verify manager has signed first
                const { data: appraisal, error: appraisalError } = await supabaseClient
                    .from('appraisals')
                    .select('manager_signature, manager_code')
                    .eq('id', appraisalId)
                    .single();

                if (appraisalError) throw appraisalError;

                if (!appraisal.manager_signature) {
                    appUtils.showNotification('Please sign as manager first', 'error');
                    return;
                }

                if (appraisal.manager_code !== currentManager.code_number) {
                    appUtils.showNotification('You are not authorized to request signature for this appraisal', 'error');
                    return;
                }

                // Update employee_signature_requested flag
                const { error: updateError } = await supabaseClient
                    .from('appraisals')
                    .update({ employee_signature_requested: true })
                    .eq('id', appraisalId);

                if (updateError) throw updateError;

                // Show success message
                appUtils.showNotification('Employee signature requested successfully', 'success');

                // Reload appraisal data
                loadAppraisalData(appraisalId);

            } catch (error) {
                console.error('Error requesting employee signature:', error);
                appUtils.showNotification('Error requesting employee signature', 'error');
            }
        }

        // Sign as employee (for managers viewing their own appraisal)
        async function signAsEmployeeSelf(appraisalId) {
            try {
                // Get current manager (acting as employee for their own appraisal)
                const currentManager = appAuth.getCurrentEmployee();
                if (!currentManager) {
                    appUtils.showNotification('Employee information not found', 'error');
                    return;
                }

                // Verify this is their own appraisal and signature is requested
                const { data: appraisal, error: appraisalError } = await supabaseClient
                    .from('appraisals')
                    .select('employee_code, employee_signature_requested, manager_signature')
                    .eq('id', appraisalId)
                    .single();

                if (appraisalError) throw appraisalError;

                // Security check: ensure this is their own appraisal
                if (appraisal.employee_code !== currentManager.code_number) {
                    appUtils.showNotification('You can only sign your own appraisal', 'error');
                    return;
                }

                // Check if signature was requested
                if (!appraisal.employee_signature_requested) {
                    appUtils.showNotification('Employee signature has not been requested yet', 'error');
                    return;
                }

                // Check if manager has signed
                if (!appraisal.manager_signature) {
                    appUtils.showNotification('Manager must sign first', 'error');
                    return;
                }

                // Update employee signature
                const { error: updateError } = await supabaseClient
                    .from('appraisals')
                    .update({
                        employee_signature: currentManager.name,
                        employee_signature_date: new Date().toISOString(),
                        updated_at: new Date().toISOString()
                    })
                    .eq('id', appraisalId);

                if (updateError) throw updateError;

                // Show success message
                appUtils.showNotification(`Signed successfully as employee`, 'success');

                // Reload appraisal data
                loadAppraisalData(appraisalId);

            } catch (error) {
                console.error('Error signing as employee:', error);
                appUtils.showNotification('Error signing as employee', 'error');
            }
        }

        // Download appraisal PDF
        async function downloadAppraisalPdf(appraisalId) {
            try {
                // Get appraisal data
                const { data: appraisal, error: appraisalError } = await supabaseClient
                    .from('appraisals')
                    .select(`
                        *,
                        employee:employees!appraisals_employee_code_fkey(*),
                        manager:employees!appraisals_manager_code_fkey(*),
                        period:appraisal_periods(*)
                    `)
                    .eq('id', appraisalId)
                    .single();
                
                if (appraisalError) throw appraisalError;
                
                // Get appraisal scores
                const { data: appraisalScores, error: scoresError } = await supabaseClient
                    .from('appraisal_scores')
                    .select(`
                        *,
                        employee_kpi:employee_kpis(
                            *,
                            kpi:kpis(
                                *,
                                category:kpi_categories(*)
                            )
                        )
                    `)
                    .eq('appraisal_id', appraisalId);
                
                if (scoresError) throw scoresError;
                
                // Get category weights
                const { data: categoryWeights } = await supabaseClient
                    .from('category_weights')
                    .select(`
                        *,
                        category:kpi_categories(*)
                    `)
                    .eq('employee_code', appraisal.employee_code);
                
                // Prepare data for PDF
                const scores = appraisalScores.map(score => ({
                    name: score.employee_kpi.kpi.name,
                    weight: score.employee_kpi.weight,
                    score: score.score,
                    comments: score.comments,
                    category: score.employee_kpi.kpi.category.name
                }));
                
                const pdfData = {
                    employee: appraisal.employee,
                    manager: appraisal.manager,
                    period: appraisal.period,
                    created_at: appraisal.created_at,
                    total_score: appraisal.total_score,
                    performance_score: appraisal.performance_score,
                    behavioral_score: appraisal.behavioral_score,
                    grade: appraisal.grade,
                    comments: appraisal.comments,
                    scores: scores,
                    category_weights: categoryWeights.map(cw => ({
                        category: cw.category.name,
                        weight: cw.weight
                    })),
                    manager_signature: appraisal.manager_signature,
                    employee_signature: appraisal.employee_signature
                };
                
                // Generate and download PDF
                const fileName = `Appraisal_${appraisal.employee.name.replace(/\s+/g, '_')}_${appraisal.period.name.replace(/\s+/g, '_')}.pdf`;
                const success = await appPDF.downloadAppraisalPDF(pdfData, fileName);
                
                if (!success) {
                    appUtils.showNotification('Error generating PDF', 'error');
                }
                
            } catch (error) {
                console.error('Error downloading PDF:', error);
                appUtils.showNotification('Error downloading PDF', 'error');
            }
        }
    </script>
    
    <style>
        .signature-box {
            border: 1px dashed var(--secondary-color);
            padding: 1rem;
            text-align: center;
            margin-top: 0.5rem;
            border-radius: var(--border-radius);
        }
        
        .signature-signed {
            color: var(--success-color);
            font-weight: bold;
        }
        
        .signature-pending {
            color: var(--warning-color);
            font-style: italic;
        }
    </style>
</body>
</html>