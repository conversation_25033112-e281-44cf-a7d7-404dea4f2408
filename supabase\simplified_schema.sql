-- HR Performance Evaluation System - Simplified Schema
-- First, drop all existing tables if they exist
DROP TABLE IF EXISTS appraisal_scores CASCADE;
DROP TABLE IF EXISTS appraisals CASCADE;
DROP TABLE IF EXISTS appraisal_periods CASCADE;
DROP TABLE IF EXISTS category_weights CASCADE;
DROP TABLE IF EXISTS employee_kpis CASCADE;
DROP TABLE IF EXISTS kpis CASCADE;
DROP TABLE IF EXISTS kpi_categories CASCADE;
DROP TABLE IF EXISTS employee_users CASCADE;
DROP TABLE IF EXISTS users CASCADE;
DROP TABLE IF EXISTS employees CASCADE;

-- Create tables with a simplified approach

-- Employees table
CREATE TABLE employees (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code_number VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    position VARCHAR(100) NOT NULL,
    department VARCHAR(100) NOT NULL,
    manager_code VARCHA<PERSON>(20) REFERENCES employees(code_number),
    is_manager <PERSON><PERSON><PERSON>EAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Users table (directly linked to employees)
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(100) NOT NULL, -- In a real app, this would be hashed
    role VARCHAR(20) NOT NULL CHECK (role IN ('admin', 'manager', 'employee')),
    employee_code VARCHAR(20) REFERENCES employees(code_number) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- KPI Categories
CREATE TABLE kpi_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- KPIs table
CREATE TABLE kpis (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    category_id UUID REFERENCES kpi_categories(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Employee KPIs (linking employees to their KPIs with weights)
CREATE TABLE employee_kpis (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_code VARCHAR(20) REFERENCES employees(code_number) ON DELETE CASCADE,
    kpi_id UUID REFERENCES kpis(id) ON DELETE CASCADE,
    weight DECIMAL(5,2) NOT NULL CHECK (weight > 0 AND weight <= 100),
    measurement TEXT, -- Custom measurement description for this employee
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(employee_code, kpi_id)
);

-- Category Weights (for performance vs behavioral split)
CREATE TABLE category_weights (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_code VARCHAR(20) REFERENCES employees(code_number) ON DELETE CASCADE,
    category_id UUID REFERENCES kpi_categories(id) ON DELETE CASCADE,
    weight DECIMAL(5,2) NOT NULL CHECK (weight > 0 AND weight <= 100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(employee_code, category_id)
);

-- Appraisal Periods
CREATE TABLE appraisal_periods (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Appraisals table
CREATE TABLE appraisals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_code VARCHAR(20) REFERENCES employees(code_number) ON DELETE CASCADE,
    manager_code VARCHAR(20) REFERENCES employees(code_number) ON DELETE CASCADE,
    period_id UUID REFERENCES appraisal_periods(id) ON DELETE CASCADE,
    total_score DECIMAL(5,2),
    performance_score DECIMAL(5,2),
    behavioral_score DECIMAL(5,2),
    grade VARCHAR(20),
    manager_comments TEXT,
    manager_signature VARCHAR(255),
    manager_signature_date TIMESTAMP WITH TIME ZONE,
    employee_signature VARCHAR(255),
    employee_signature_date TIMESTAMP WITH TIME ZONE,
    employee_signature_requested BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(employee_code, period_id)
);

-- Appraisal Scores (individual KPI scores) - DENORMALIZED FOR HISTORICAL INTEGRITY
CREATE TABLE appraisal_scores (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    appraisal_id UUID REFERENCES appraisals(id) ON DELETE CASCADE,
    employee_kpi_id UUID REFERENCES employee_kpis(id) ON DELETE SET NULL,
    -- DENORMALIZED KPI DATA (snapshot at time of appraisal creation)
    -- This is the key to historical data integrity!
    kpi_name VARCHAR(255) NOT NULL,
    kpi_description TEXT,
    kpi_category_name VARCHAR(100) NOT NULL,
    kpi_weight DECIMAL(5,2) NOT NULL,
    kpi_measurement TEXT,
    score INTEGER NOT NULL CHECK (score >= 1 AND score <= 5),
    comments TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(appraisal_id, employee_kpi_id)
);

-- Appraisal Assignments (admin-assigned appraisals tracking)
CREATE TABLE appraisal_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_code VARCHAR(20) REFERENCES employees(code_number) ON DELETE CASCADE,
    period_id UUID REFERENCES appraisal_periods(id) ON DELETE CASCADE,
    assigned_by VARCHAR(20) REFERENCES employees(code_number) ON DELETE SET NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed')),
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    UNIQUE(employee_code, period_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_appraisal_assignments_status ON appraisal_assignments(status);
CREATE INDEX IF NOT EXISTS idx_appraisal_assignments_period ON appraisal_assignments(period_id);
CREATE INDEX IF NOT EXISTS idx_appraisal_assignments_employee ON appraisal_assignments(employee_code);

-- Insert KPI Categories
INSERT INTO kpi_categories (name, description) VALUES
('Performance', 'KPIs related to job performance and deliverables'),
('Behavioral', 'KPIs related to behavior, attitude, and soft skills');

-- Insert default category weights for all employees (80% Performance, 20% Behavioral)
INSERT INTO category_weights (employee_code, category_id, weight) 
SELECT e.code_number, k.id, 
    CASE WHEN k.name = 'Performance' THEN 80 ELSE 20 END
FROM employees e, kpi_categories k;

-- Insert test employees
INSERT INTO employees (code_number, name, position, department, is_manager) VALUES
('EMP001', 'Admin User', 'HR Administrator', 'Human Resources', TRUE),
('EMP002', 'John Smith', 'Senior Manager', 'Operations', TRUE),
('EMP003', 'Jane Doe', 'Team Lead', 'Operations', TRUE),
('EMP004', 'Bob Johnson', 'Analyst', 'Operations', FALSE),
('EMP005', 'Alice Brown', 'Developer', 'IT', FALSE),
('100', 'Ahmed Atef', 'Senior Developer', 'IT', FALSE);

-- Set manager relationships
UPDATE employees SET manager_code = NULL WHERE code_number = 'EMP001'; -- Admin has no manager
UPDATE employees SET manager_code = NULL WHERE code_number = 'EMP002'; -- Senior Manager has no manager
UPDATE employees SET manager_code = 'EMP002' WHERE code_number = 'EMP003'; -- Team Lead reports to Senior Manager
UPDATE employees SET manager_code = 'EMP003' WHERE code_number = 'EMP004'; -- Analyst reports to Team Lead
UPDATE employees SET manager_code = 'EMP003' WHERE code_number = 'EMP005'; -- Developer reports to Team Lead
UPDATE employees SET manager_code = 'EMP003' WHERE code_number = '100'; -- Ahmed Atef reports to Team Lead

-- Insert test users (admin, managers, and employees)
INSERT INTO users (username, password, role, employee_code) VALUES
('admin', 'admin123', 'admin', 'EMP001'),
('parent_manager', 'manager123', 'manager', 'EMP002'),
('child_manager', 'manager456', 'manager', 'EMP003'),
('ahmed_atef', 'employee123', 'employee', '100'),
('alice_brown', 'employee123', 'employee', 'EMP005'),
('bob_johnson', 'employee123', 'employee', 'EMP004');

-- Get category IDs for later use
DO $$
DECLARE
    performance_category_id UUID;
    behavioral_category_id UUID;
BEGIN
    SELECT id INTO performance_category_id FROM kpi_categories WHERE name = 'Performance';
    SELECT id INTO behavioral_category_id FROM kpi_categories WHERE name = 'Behavioral';

    -- Insert sample KPIs
    INSERT INTO kpis (name, description, category_id) VALUES
    ('Project Delivery', 'Ability to deliver projects on time and within budget', performance_category_id),
    ('Quality of Work', 'Quality and accuracy of deliverables', performance_category_id),
    ('Technical Skills', 'Proficiency in required technical skills', performance_category_id),
    ('Problem Solving', 'Ability to solve complex problems', performance_category_id),
    ('Communication', 'Effectiveness in verbal and written communication', behavioral_category_id),
    ('Teamwork', 'Ability to work effectively in a team', behavioral_category_id),
    ('Initiative', 'Taking initiative and being proactive', behavioral_category_id),
    ('Adaptability', 'Ability to adapt to changes', behavioral_category_id);
END $$;

-- Assign KPIs to employees with weights
-- For Bob Johnson (Analyst) - Performance KPIs total 100%, Behavioral KPIs total 100%
INSERT INTO employee_kpis (employee_code, kpi_id, weight)
SELECT 'EMP004', id,
    CASE
        WHEN name = 'Technical Skills' THEN 40
        WHEN name = 'Quality of Work' THEN 35
        WHEN name = 'Problem Solving' THEN 25
        WHEN name = 'Communication' THEN 40
        WHEN name = 'Teamwork' THEN 35
        WHEN name = 'Initiative' THEN 25
    END
FROM kpis
WHERE name IN ('Quality of Work', 'Technical Skills', 'Problem Solving', 'Communication', 'Teamwork', 'Initiative');

-- For Alice Brown (Developer) - Performance KPIs total 100%, Behavioral KPIs total 100%
INSERT INTO employee_kpis (employee_code, kpi_id, weight)
SELECT 'EMP005', id,
    CASE
        WHEN name = 'Technical Skills' THEN 40
        WHEN name = 'Problem Solving' THEN 35
        WHEN name = 'Quality of Work' THEN 15
        WHEN name = 'Project Delivery' THEN 10
        WHEN name = 'Communication' THEN 40
        WHEN name = 'Teamwork' THEN 35
        WHEN name = 'Adaptability' THEN 25
    END
FROM kpis
WHERE name IN ('Quality of Work', 'Technical Skills', 'Problem Solving', 'Project Delivery', 'Communication', 'Teamwork', 'Adaptability');

-- For Ahmed Atef (employee 100) - Performance KPIs total 100%, Behavioral KPIs total 100%
INSERT INTO employee_kpis (employee_code, kpi_id, weight)
SELECT '100', id,
    CASE
        WHEN name = 'Technical Skills' THEN 50
        WHEN name = 'Quality of Work' THEN 30
        WHEN name = 'Problem Solving' THEN 20
        WHEN name = 'Communication' THEN 40
        WHEN name = 'Teamwork' THEN 30
        WHEN name = 'Initiative' THEN 30
    END
FROM kpis
WHERE name IN ('Quality of Work', 'Technical Skills', 'Problem Solving', 'Communication', 'Teamwork', 'Initiative');

-- For Jane Doe (EMP003) - Performance KPIs total 100%, Behavioral KPIs total 100%
INSERT INTO employee_kpis (employee_code, kpi_id, weight)
SELECT 'EMP003', id,
    CASE
        WHEN name = 'Quality of Work' THEN 30
        WHEN name = 'Technical Skills' THEN 30
        WHEN name = 'Problem Solving' THEN 40
        WHEN name = 'Communication' THEN 40
        WHEN name = 'Teamwork' THEN 35
        WHEN name = 'Adaptability' THEN 25
    END
FROM kpis
WHERE name IN ('Quality of Work', 'Technical Skills', 'Problem Solving', 'Communication', 'Teamwork', 'Adaptability');

-- For Admin User (EMP001) - Performance KPIs total 100%, Behavioral KPIs total 100%
INSERT INTO employee_kpis (employee_code, kpi_id, weight)
SELECT 'EMP001', id,
    CASE
        WHEN name = 'Quality of Work' THEN 40
        WHEN name = 'Problem Solving' THEN 35
        WHEN name = 'Project Delivery' THEN 25
        WHEN name = 'Communication' THEN 50
        WHEN name = 'Initiative' THEN 30
        WHEN name = 'Adaptability' THEN 20
    END
FROM kpis
WHERE name IN ('Quality of Work', 'Problem Solving', 'Project Delivery', 'Communication', 'Initiative', 'Adaptability');

-- For John Smith (EMP002) - Performance KPIs total 100%, Behavioral KPIs total 100%
INSERT INTO employee_kpis (employee_code, kpi_id, weight)
SELECT 'EMP002', id,
    CASE
        WHEN name = 'Project Delivery' THEN 45
        WHEN name = 'Quality of Work' THEN 30
        WHEN name = 'Problem Solving' THEN 25
        WHEN name = 'Communication' THEN 40
        WHEN name = 'Initiative' THEN 35
        WHEN name = 'Adaptability' THEN 25
    END
FROM kpis
WHERE name IN ('Project Delivery', 'Quality of Work', 'Problem Solving', 'Communication', 'Initiative', 'Adaptability');

-- Set category weights for employees
INSERT INTO category_weights (employee_code, category_id, weight)
SELECT 'EMP004', id, 
    CASE 
        WHEN name = 'Performance' THEN 80
        ELSE 20
    END
FROM kpi_categories;

INSERT INTO category_weights (employee_code, category_id, weight)
SELECT 'EMP005', id,
    CASE
        WHEN name = 'Performance' THEN 70
        ELSE 30
    END
FROM kpi_categories;

INSERT INTO category_weights (employee_code, category_id, weight)
SELECT 'EMP003', id,
    CASE
        WHEN name = 'Performance' THEN 80
        ELSE 20
    END
FROM kpi_categories;

INSERT INTO category_weights (employee_code, category_id, weight)
SELECT '100', id,
    CASE
        WHEN name = 'Performance' THEN 75
        ELSE 25
    END
FROM kpi_categories;

INSERT INTO category_weights (employee_code, category_id, weight)
SELECT 'EMP001', id,
    CASE
        WHEN name = 'Performance' THEN 70
        ELSE 30
    END
FROM kpi_categories;

INSERT INTO category_weights (employee_code, category_id, weight)
SELECT 'EMP002', id,
    CASE
        WHEN name = 'Performance' THEN 75
        ELSE 25
    END
FROM kpi_categories;

-- Insert appraisal periods (using current year)
INSERT INTO appraisal_periods (name, start_date, end_date, is_active) VALUES
('Q1 2025', '2025-01-01', '2025-03-31', TRUE),
('Q2 2025', '2025-04-01', '2025-06-30', TRUE),
('Q3 2025', '2025-07-01', '2025-09-30', TRUE),
('Q4 2025', '2025-10-01', '2025-12-31', TRUE),
('Semester 1 2025', '2025-01-01', '2025-06-30', TRUE),
('Annual 2025', '2025-01-01', '2025-12-31', TRUE);

-- ============================================================================
-- CLEAN SCHEMA - NO TEST DATA
-- ============================================================================
-- This schema is now ready for use in new projects without hardcoded test data.
-- All sample appraisals and scores have been removed to prevent conflicts
-- when setting up new Performance Management System instances.
--
-- To add test data for development:
-- 1. Create employees, KPIs, and periods through the admin interface
-- 2. Use the system to create appraisals naturally
-- 3. This ensures data consistency and proper grade calculations
-- ============================================================================

-- KPI Weight Validation Rules:
-- 1. Performance KPIs must total exactly 100% for each employee
-- 2. Behavioral KPIs must total exactly 100% for each employee
-- 3. Admin interface provides real-time weight tracking and validation
-- 4. Use "Validate All" button to check all categories before finalizing assignments