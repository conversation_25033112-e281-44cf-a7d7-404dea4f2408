<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Appraisal - Performance Evaluation System</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- jsPDF -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/styles.css">

    <style>
        /* Modern card styling */
        .card-header {
            background: linear-gradient(135deg, #4a90e2 0%, #7bb3f0 100%);
            color: white;
            border-radius: 8px 8px 0 0;
            border: none;
            padding: 10px 15px;
        }

        .card-body {
            padding: 10px;
        }

        /* Compact chart containers */
        .chart-container-compact {
            position: relative;
            height: 280px;
            margin: 10px 0;
        }

        body.dark-mode .table {
            color: #e0e0e0;
        }

        body.dark-mode .table thead th {
            background-color: rgba(255, 255, 255, 0.05);
            color: #e0e0e0;
        }

        body.dark-mode .form-label {
            color: #e0e0e0;
        }

        body.dark-mode h1, body.dark-mode h2, body.dark-mode h3, body.dark-mode h5 {
            color: #e0e0e0;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container header-container">
            <div class="logo">
                <i class="fas fa-chart-line logo-icon"></i>
                Performance Evaluation System
            </div>
            <nav class="nav">
                <a href="index.html" class="nav-link">Dashboard</a>
                <a href="employees.html" class="nav-link">Employees</a>
                <a href="kpi-assignment.html" class="nav-link">KPI Assignment</a>
                <a href="appraisal-assignment.html" class="nav-link">Appraisal Assignment</a>
                <a href="reports.html" class="nav-link active">Reports</a>
                <a href="#" id="logoutBtn" class="nav-link">Logout</a>
            </nav>
        </div>
    </header>

    <!-- Main content -->
    <main class="main">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="text-2xl font-bold">Appraisal Report</h1>
                <div>
                    <a href="reports.html" class="btn btn-secondary mr-2">
                        <i class="fas fa-arrow-left mr-1"></i> Back to Reports
                    </a>
                    <button id="downloadPdfBtn" class="btn btn-primary">
                        <i class="fas fa-download mr-1"></i> Download PDF
                    </button>
                </div>
            </div>

            <div class="appraisal-report">
                <div class="report-header">
                    <h2 class="report-title">Performance Appraisal Report</h2>
                    <p class="report-subtitle" id="reportPeriod"></p>

                    <div class="report-info">
                        <div class="info-item">
                            <div class="info-label">Employee</div>
                            <div id="employeeName"></div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">Position</div>
                            <div id="employeePosition"></div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">Department</div>
                            <div id="employeeDepartment"></div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">Manager</div>
                            <div id="managerName"></div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">Date</div>
                            <div id="appraisalDate"></div>
                        </div>
                    </div>
                </div>

                <!-- Charts Section -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header py-2">
                                <h5 class="mb-0">Total Score</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container-compact">
                                    <canvas id="totalScoreGauge"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header py-2">
                                <h5 class="mb-0">Performance</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container-compact">
                                    <canvas id="performanceGauge"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header py-2">
                                <h5 class="mb-0">Behavioral</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container-compact">
                                    <canvas id="behavioralGauge"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="score-summary">
                    <div>
                        <span class="summary-label">Total Score:</span>
                        <span id="totalScore" class="summary-value"></span>
                    </div>
                    <div>
                        <span class="summary-label">Grade:</span>
                        <span id="gradeValue" class="summary-value"></span>
                    </div>
                </div>

                <!-- Performance KPIs Section -->
                <div class="report-section">
                    <h3 class="report-section-title">Performance KPIs</h3>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>KPI</th>
                                    <th>Weight</th>
                                    <th>Score</th>
                                    <th>Weighted Score</th>
                                    <th>Comments</th>
                                </tr>
                            </thead>
                            <tbody id="performanceKpisTable">
                                <!-- Performance KPIs will be loaded dynamically -->
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="3">Performance Total</th>
                                    <th id="performanceTotal"></th>
                                    <th></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>

                <!-- Behavioral KPIs Section -->
                <div class="report-section">
                    <h3 class="report-section-title">Behavioral KPIs</h3>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>KPI</th>
                                    <th>Weight</th>
                                    <th>Score</th>
                                    <th>Weighted Score</th>
                                    <th>Comments</th>
                                </tr>
                            </thead>
                            <tbody id="behavioralKpisTable">
                                <!-- Behavioral KPIs will be loaded dynamically -->
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="3">Behavioral Total</th>
                                    <th id="behavioralTotal"></th>
                                    <th></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>

                <!-- Category Weights Section -->
                <div class="report-section">
                    <h3 class="report-section-title">Category Weights</h3>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Category</th>
                                    <th>Weight</th>
                                    <th>Score</th>
                                    <th>Weighted Score</th>
                                </tr>
                            </thead>
                            <tbody id="categoryWeightsTable">
                                <!-- Category weights will be loaded dynamically -->
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="3">Overall Total</th>
                                    <th id="overallTotal"></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>

                <!-- Comments Section -->
                <div class="report-section">
                    <h3 class="report-section-title">Comments</h3>
                    <div class="card">
                        <div class="card-body" id="commentsSection">
                            <!-- Comments will be loaded dynamically -->
                        </div>
                    </div>
                </div>

                <!-- Signatures Section -->
                <div class="report-section">
                    <h3 class="report-section-title">Signatures</h3>
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Manager Signature</label>
                                <div id="managerSignature" class="signature-box">
                                    <!-- Manager signature will be loaded dynamically -->
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Employee Signature</label>
                                <div id="employeeSignature" class="signature-box">
                                    <!-- Employee signature will be loaded dynamically -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Supabase JS -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2/dist/umd/supabase.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/supabase.js"></script>
    <script src="../assets/js/utils.js"></script>
    <script src="../assets/js/auth.js"></script>
    <script src="../assets/js/charts.js"></script>
    <script src="../assets/js/pdf.js"></script>

    <script>
        let currentAppraisal = null;

        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication
            if (!appAuth.checkAuth('admin')) {
                return;
            }

            // Initialize enhanced navigation
            appUtils.initializeNavigation();

            // Logout button
            document.getElementById('logoutBtn').addEventListener('click', function(e) {
                e.preventDefault();
                appAuth.logoutUser();
            });

            // Download PDF button
            document.getElementById('downloadPdfBtn').addEventListener('click', function() {
                if (currentAppraisal) {
                    downloadAppraisalPdf();
                }
            });

            // Load appraisal data
            loadAppraisalData();
        });

        // Global chart instances for cleanup
        let totalScoreChart = null;
        let performanceChart = null;
        let behavioralChart = null;

        // Load appraisal data
        async function loadAppraisalData() {
            try {
                // Destroy existing charts before creating new ones
                if (totalScoreChart) {
                    totalScoreChart.destroy();
                    totalScoreChart = null;
                }
                if (performanceChart) {
                    performanceChart.destroy();
                    performanceChart = null;
                }
                if (behavioralChart) {
                    behavioralChart.destroy();
                    behavioralChart = null;
                }
                // Get appraisal ID from URL
                const urlParams = new URLSearchParams(window.location.search);
                const appraisalId = urlParams.get('id');

                if (!appraisalId) {
                    appUtils.showNotification('No appraisal ID provided', 'error');
                    window.location.href = 'reports.html';
                    return;
                }

                // Get appraisal data with all related information
                const { data: appraisal, error: appraisalError } = await supabaseClient
                    .from('appraisals')
                    .select(`
                        *,
                        employee:employees!appraisals_employee_code_fkey(*),
                        manager:employees!appraisals_manager_code_fkey(*),
                        period:appraisal_periods(*)
                    `)
                    .eq('id', appraisalId)
                    .single();

                if (appraisalError) throw appraisalError;

                if (!appraisal) {
                    appUtils.showNotification('Appraisal not found', 'error');
                    window.location.href = 'reports.html';
                    return;
                }

                currentAppraisal = appraisal;

                // Display appraisal information
                document.getElementById('reportPeriod').textContent = appraisal.period ? appraisal.period.name : 'N/A';
                document.getElementById('employeeName').textContent = appraisal.employee ? appraisal.employee.name : 'N/A';
                document.getElementById('employeePosition').textContent = appraisal.employee ? appraisal.employee.position : 'N/A';
                document.getElementById('employeeDepartment').textContent = appraisal.employee ? appraisal.employee.department : 'N/A';
                document.getElementById('managerName').textContent = appraisal.manager ? appraisal.manager.name : 'N/A';
                document.getElementById('appraisalDate').textContent = appUtils.formatReadableDate(appraisal.created_at);

                // Display scores and grade
                document.getElementById('totalScore').textContent = `${appraisal.total_score.toFixed(1)}%`;
                document.getElementById('gradeValue').textContent = appraisal.grade;
                document.getElementById('gradeValue').className = `summary-value grade-${appraisal.grade.toLowerCase().replace(' ', '-')}`;

                // Create gauge charts and store instances for cleanup
                totalScoreChart = appCharts.createGaugeChart('totalScoreGauge', appraisal.total_score, 'Overall Performance');
                performanceChart = appCharts.createGaugeChart('performanceGauge', appraisal.performance_score, 'Performance');
                behavioralChart = appCharts.createGaugeChart('behavioralGauge', appraisal.behavioral_score, 'Behavioral');

                // Get appraisal scores
                const { data: appraisalScores, error: scoresError } = await supabaseClient
                    .from('appraisal_scores')
                    .select(`
                        *,
                        employee_kpi:employee_kpis(
                            *,
                            kpi:kpis(
                                *,
                                category:kpi_categories(*)
                            )
                        )
                    `)
                    .eq('appraisal_id', appraisalId);

                if (scoresError) throw scoresError;

                // Sort scores by employee_kpi created_at to match create-appraisal order
                appraisalScores.sort((a, b) => {
                    const dateA = new Date(a.employee_kpi?.created_at || 0);
                    const dateB = new Date(b.employee_kpi?.created_at || 0);
                    return dateA - dateB;
                });

                // Separate scores by category
                const performanceScores = [];
                const behavioralScores = [];

                appraisalScores.forEach(score => {
                    if (score.employee_kpi && score.employee_kpi.kpi && score.employee_kpi.kpi.category) {
                        const category = score.employee_kpi.kpi.category.name;

                        if (category === 'Performance') {
                            performanceScores.push({
                                id: score.id,
                                name: score.employee_kpi.kpi.name,
                                weight: score.employee_kpi.weight,
                                score: score.score,
                                comments: score.comments
                            });
                        } else if (category === 'Behavioral') {
                            behavioralScores.push({
                                id: score.id,
                                name: score.employee_kpi.kpi.name,
                                weight: score.employee_kpi.weight,
                                score: score.score,
                                comments: score.comments
                            });
                        }
                    }
                });

                // Display KPI scores
                displayKpiScores('performanceKpisTable', performanceScores);
                displayKpiScores('behavioralKpisTable', behavioralScores);

                // Display category totals
                document.getElementById('performanceTotal').textContent = `${appraisal.performance_score.toFixed(1)}%`;
                document.getElementById('behavioralTotal').textContent = `${appraisal.behavioral_score.toFixed(1)}%`;

                // Get category weights
                const { data: categoryWeights } = await supabaseClient
                    .from('category_weights')
                    .select(`
                        *,
                        category:kpi_categories(*)
                    `)
                    .eq('employee_code', appraisal.employee_code);

                // Display category weights
                displayCategoryWeights('categoryWeightsTable', categoryWeights, appraisal);

                document.getElementById('overallTotal').textContent = `${appraisal.total_score.toFixed(1)}%`;

                // Display comments
                document.getElementById('commentsSection').textContent = appraisal.comments || 'No comments provided.';

                // Update signature display (admin view only - no signing functionality)
                updateSignatureDisplay(appraisal);

            } catch (error) {
                console.error('Error loading appraisal data:', error);
                appUtils.showNotification('Error loading appraisal data: ' + error.message, 'error');
            }
        }

        // Display KPI scores
        function displayKpiScores(tableId, scores) {
            const tableBody = document.getElementById(tableId);
            tableBody.innerHTML = '';

            if (scores.length === 0) {
                const row = document.createElement('tr');
                const cell = document.createElement('td');
                cell.colSpan = 5;
                cell.textContent = 'No KPIs found for this category';
                cell.className = 'text-center';
                row.appendChild(cell);
                tableBody.appendChild(row);
                return;
            }

            scores.forEach(score => {
                const row = document.createElement('tr');

                // KPI Name
                const nameCell = document.createElement('td');
                nameCell.textContent = score.name;
                row.appendChild(nameCell);

                // Weight
                const weightCell = document.createElement('td');
                weightCell.textContent = `${score.weight}%`;
                row.appendChild(weightCell);

                // Score
                const scoreCell = document.createElement('td');
                scoreCell.textContent = `${score.score}/5`;
                row.appendChild(scoreCell);

                // Weighted Score
                const weightedScore = appUtils.calculateWeightedScore(score.score, score.weight);
                const weightedScoreCell = document.createElement('td');
                weightedScoreCell.textContent = `${weightedScore.toFixed(1)}`;
                row.appendChild(weightedScoreCell);

                // Comments
                const commentsCell = document.createElement('td');
                commentsCell.textContent = score.comments || 'No comments';
                row.appendChild(commentsCell);

                tableBody.appendChild(row);
            });
        }

        // Display category weights
        function displayCategoryWeights(tableId, categoryWeights, appraisal) {
            const tableBody = document.getElementById(tableId);
            tableBody.innerHTML = '';

            if (!categoryWeights || categoryWeights.length === 0) {
                const row = document.createElement('tr');
                const cell = document.createElement('td');
                cell.colSpan = 4;
                cell.textContent = 'No category weights found';
                cell.className = 'text-center';
                row.appendChild(cell);
                tableBody.appendChild(row);
                return;
            }

            categoryWeights.forEach(categoryWeight => {
                if (categoryWeight.category) {
                    const row = document.createElement('tr');

                    // Category Name
                    const nameCell = document.createElement('td');
                    nameCell.textContent = categoryWeight.category.name;
                    row.appendChild(nameCell);

                    // Weight
                    const weightCell = document.createElement('td');
                    weightCell.textContent = `${categoryWeight.weight}%`;
                    row.appendChild(weightCell);

                    // Score
                    const scoreCell = document.createElement('td');
                    const score = categoryWeight.category.name === 'Performance' ?
                        appraisal.performance_score :
                        appraisal.behavioral_score;

                    scoreCell.textContent = `${score.toFixed(1)}%`;
                    row.appendChild(scoreCell);

                    // Weighted Score
                    const weightedScore = (score * (categoryWeight.weight / 100));
                    const weightedScoreCell = document.createElement('td');
                    weightedScoreCell.textContent = `${weightedScore.toFixed(1)}%`;
                    row.appendChild(weightedScoreCell);

                    tableBody.appendChild(row);
                }
            });
        }

        // Update signature display (admin view only)
        function updateSignatureDisplay(appraisal) {
            const managerSignatureBox = document.getElementById('managerSignature');
            const employeeSignatureBox = document.getElementById('employeeSignature');

            // Manager signature
            if (appraisal.manager_signature) {
                managerSignatureBox.innerHTML = `
                    <div class="signature-signed">
                        <i class="fas fa-check-circle text-success"></i>
                        <span>Signed by ${appraisal.manager.name}</span>
                    </div>
                `;
            } else {
                managerSignatureBox.innerHTML = `
                    <div class="signature-pending">
                        <i class="fas fa-clock text-warning"></i>
                        <span>Pending signature</span>
                    </div>
                `;
            }

            // Employee signature - ADMIN OVERRIDE CAPABILITY
            if (appraisal.employee_signature) {
                employeeSignatureBox.innerHTML = `
                    <div class="signature-signed">
                        <i class="fas fa-check-circle text-success"></i>
                        <span>Signed by ${appraisal.employee.name}</span>
                    </div>
                `;
            } else if (appraisal.employee_signature_requested) {
                // Show admin override button when signature is requested
                employeeSignatureBox.innerHTML = `
                    <div class="signature-pending mb-2">
                        <i class="fas fa-clock text-warning"></i>
                        <span>Pending Employee Signature</span>
                    </div>
                    <button id="adminSignAsEmployee" class="btn btn-danger btn-sm">
                        <i class="fas fa-user-shield mr-1"></i> Sign as Employee (Admin Override)
                    </button>
                `;

                // Add event listener
                document.getElementById('adminSignAsEmployee').addEventListener('click', function() {
                    adminSignAsEmployee(appraisal.id);
                });
            } else {
                employeeSignatureBox.innerHTML = `
                    <div class="signature-pending">
                        <i class="fas fa-clock text-warning"></i>
                        <span>Awaiting manager request</span>
                    </div>
                `;
            }
        }

        // Admin sign as employee - ADMIN OVERRIDE FUNCTION
        async function adminSignAsEmployee(appraisalId) {
            try {
                // Verify admin authority (basic check)
                const currentUser = appAuth.getCurrentUser();
                if (!currentUser) {
                    appUtils.showNotification('User information not found', 'error');
                    return;
                }

                // Verify signature has been requested and manager has signed
                const { data: appraisal, error: appraisalError } = await supabaseClient
                    .from('appraisals')
                    .select('employee_signature_requested, manager_signature, employee:employees!appraisals_employee_code_fkey(name)')
                    .eq('id', appraisalId)
                    .single();

                if (appraisalError) throw appraisalError;

                if (!appraisal.employee_signature_requested) {
                    appUtils.showNotification('Employee signature has not been requested yet', 'error');
                    return;
                }

                if (!appraisal.manager_signature) {
                    appUtils.showNotification('Manager must sign first', 'error');
                    return;
                }

                // Confirm admin override action
                if (!confirm(`Are you sure you want to sign on behalf of ${appraisal.employee.name}? This action will complete the appraisal.`)) {
                    return;
                }

                // Update employee signature
                const { error: updateError } = await supabaseClient
                    .from('appraisals')
                    .update({ employee_signature: true })
                    .eq('id', appraisalId);

                if (updateError) throw updateError;

                // Show success message
                appUtils.showNotification(`Signed successfully on behalf of ${appraisal.employee.name}`, 'success');

                // Reload appraisal data
                loadAppraisalData(appraisalId);

            } catch (error) {
                console.error('Error signing appraisal as admin:', error);
                appUtils.showNotification('Error signing appraisal', 'error');
            }
        }

        // Download appraisal PDF
        function downloadAppraisalPdf() {
            try {
                if (!currentAppraisal) {
                    appUtils.showNotification('Appraisal data not loaded', 'error');
                    return;
                }

                // Generate PDF
                const pdfTitle = `Appraisal_${currentAppraisal.employee.name}_${currentAppraisal.period.name}`.replace(/\s+/g, '_');
                appPdf.generateAppraisalPdf(currentAppraisal, pdfTitle);

            } catch (error) {
                console.error('Error downloading PDF:', error);
                appUtils.showNotification('Error downloading PDF: ' + error.message, 'error');
            }
        }
    </script>
</body>
</html>
